<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="Shinsoft.Core">
      <HintPath>..\..\00.Reference\net8.0\Shinsoft.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\10.EntityFrameworkCore\Shinsoft.DDI.Entities\Shinsoft.DDI.Entities.csproj" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
	<PackageReference Include="EPPlus" Version="4.5.3.3" />
	<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
	<PackageReference Include="NLog" Version="5.4.0" />
	<PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
  </ItemGroup>

  <ItemGroup>
	<Using Include="System.ComponentModel" />
	<Using Include="Shinsoft.Core" />
	<Using Include="Shinsoft.Core.Json" />
	<Using Include="Shinsoft.DDI.Entities" />
	<Using Include="Shinsoft.DDI.Common.Configration" />
  </ItemGroup>

</Project>
