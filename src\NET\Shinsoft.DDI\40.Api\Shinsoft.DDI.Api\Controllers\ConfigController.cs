﻿using Microsoft.AspNetCore.Mvc;
using NLog.Filters;

namespace Shinsoft.DDI.Api.Controllers
{
    [ApiExplorerSettings(GroupName = "配置与监控")]
    public class ConfigController :  BaseApiController<ConfigBll>
    {
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询经销商配置信息")]
        public QueryResult<ReceiverClientQuery> QueryReceiverClient([FromQuery] ReceiverClientFilter filter)
        {
            var exps = this.NewExps<ReceiverClient>();

            return this.Repo.GetDynamicQuery<ReceiverClient, ReceiverClientQuery>(filter, exps);
        }
    }
}
