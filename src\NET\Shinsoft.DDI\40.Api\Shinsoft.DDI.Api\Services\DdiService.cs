﻿using Microsoft.Extensions.Primitives;
using Microsoft.Net.Http.Headers;
using Shinsoft.DDI.Api.App_Start.Interfaces;
using System;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;

namespace Shinsoft.DDI.Api
{
    [AllowAnonymous, SdrAuthorize]
    [Route("ddi/[action]")]
    [ApiExplorerSettings(GroupName = "DDI")]
    public class DdiService : BaseApiController<DdiBll>, IReceiverController
    {
        #region IReceiverController

        string IReceiverController.SdrCode => this.SdrCode;

        Receiver? IReceiverController.RawReceiver
        {
            get => _receiver;
            set => _receiver = value;
        }

        #endregion IReceiverController

        #region Receiver

        protected virtual string SdrCode => this.Request.Headers[Config.SDR.CodeHeader].AsString();

        private Receiver? _receiver = null;

        protected virtual Receiver CurrentReceiver => _receiver ??= this.GetCurrentReceiver();

        protected virtual Receiver GetCurrentReceiver()
        {
            var sdrCode = this.SdrCode;

            if (sdrCode.IsEmpty())
            {
                throw new Exception("获取经销商授权码失败");
            }

            var repo = this.GetRepo<SysBll>(Config.DefaultCompanyId);

            var receiver = repo.GetEntity<Receiver>(p => p.SdrCode == this.SdrCode);

            if (receiver == null)
            {
                throw new Exception("获取经销商失败");
            }

            return receiver;
        }

        protected override Guid? OperatorCompanyId => null;

        #endregion Receiver

        #region 测试

        /// <summary>
        /// 加密SDR令牌
        /// </summary>
        [SdrAuthorize(false)]
        [HttpGet]
        public string EncryptSdrToken([FromQuery] string sdrCode, [FromQuery] DateTime? day = null)
        {
            return Config.Debug
                ? SdrAuthHelper.EncryptSdrToken(sdrCode, day)
                : "只允许测试环境调用";
        }

        /// <summary>
        /// 验证SDR令牌
        /// </summary>
        [SdrAuthorize(false)]
        [HttpGet]
        public bool ValdateSdrToken([FromQuery] string sdrCode, [FromQuery] string sdrToken, [FromQuery] DateTime? day = null)
        {
            return SdrAuthHelper.ValdateSdrToken(sdrCode, sdrToken, day);
        }

        /// <summary>
        /// 测试SDR令牌
        /// </summary>
        [HttpGet]
        public IActionResult TestSdrToken()
        {
            return this.Xml(this.CurrentReceiver.Map<ReceiverModel>());
        }

        #endregion 测试

        /// <summary>
        /// 校验经销商授权码
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Interface, Operate = "获取经销商配置")]
        public IActionResult VerifyCode([FromForm] IFormCollection form)
        {
            object? result = null;

            if (this.CurrentReceiver.Client == null)
            {
                ClientResult verifyException = new ClientResult()
                {
                    resHead = new ClientResultInfo()
                    {
                        resCode = this.CurrentReceiver.Code,
                        resMsg = "该经销商未配置"
                    }
                };
            }
            else
            {
                var version = form["version"].ToString();

                result = this.CurrentReceiver.Client.Map<ReceiverCfg>();
            }

            return this.Xml(result);
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Interface, Operate = "保存数据", Mask = ApiMask.Input)]
        public string SaveData([FromForm] IFormCollection form)
        {
            //流向类型（B/S/I），B-买进、S-卖出、I-库存
            var dataType = form["dataType"].ToString();
            //流向数据，XML格式
            var dataXml = form["dataXml"].ToString();
            //分配给经销商的WS存数帐号
            var user = form["user"].ToString();
            //分配给经销商的WS存数密码
            var pwd = form["pwd"].ToString();

            //校验参数有效性
            var errors = new List<string>();

            if (dataType.IsEmpty())
            {
                errors.Add("流向类型不能空");
            }
            else
            {
                if (dataType != SalesFlowDataType.B.ToString() || dataType != SalesFlowDataType.S.ToString() || dataType != SalesFlowDataType.I.ToString())
                {
                    errors.Add("流向类型格式不正确");
                }
            }

            if (dataXml.IsEmpty())
            {
                errors.Add("流向数据不能空");
            }
            else
            {
                //TODO 验证是否为有效的XML格式
            }
            if (user.IsEmpty())
            {
                errors.Add("帐号不能为空");
            }

            if (pwd.IsEmpty())
            {
                errors.Add("密码不能为空");
            }

            //TODO 校验用户名密码是否为有效数据

            if (errors.Any())
            {
                ClientResult verifyException = new ClientResult()
                {
                    resHead = new ClientResultInfo()
                    {
                        resCode = this.CurrentReceiver.Code,
                        resMsg = string.Join(";", errors)
                    }
                };
                var xml = verifyException.ToXml();

                return xml;
            }

            //TODO 调用业务方法，处理数据
            this.Repo.SaveClientData((SalesFlowDataType)Enum.Parse(typeof(SalesFlowDataType), dataType), dataXml);

            return string.Empty;
        }

        /// <summary>
        /// 保存日志到服务端
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Interface, Operate = "保存日志到服务端")]
        public string SaveLog([FromForm] IFormCollection form)
        {
            //日志内容
            var log = form["log"].ToString();
            //日志时间
            var logtime = form["logtime"].ToString();
            //分配给经销商的日志帐号
            var user = form["user"].ToString();
            //分配给经销商的日志密码
            var pwd = form["pwd"].ToString();

            //校验参数有效性
            var errors = new List<string>();

            if (log.IsEmpty())
            {
                errors.Add("日志内容不能空");
            }
            if (logtime.IsEmpty())
            {
                errors.Add("日志时间不能空");
            }
            if (user.IsEmpty())
            {
                errors.Add("帐号不能为空");
            }
            if (pwd.IsEmpty())
            {
                errors.Add("密码不能为空");
            }

            //TODO 校验用户有效性

            if (errors.Any())
            {
                ClientResult verifyException = new ClientResult()
                {
                    resHead = new ClientResultInfo()
                    {
                        resCode = this.CurrentReceiver.Code,
                        resMsg = string.Join(";", errors)
                    }
                };
                var xml = verifyException.ToXml();

                return xml;
            }

            //调用业务处理数据
            this.Repo.SaveClientLog(log, logtime);

            return string.Empty;
        }
    }
}