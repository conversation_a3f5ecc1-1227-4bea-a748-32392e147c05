﻿using Shinsoft.DDI.Api.App_Start.Interfaces;
using System.Runtime.CompilerServices;

namespace Shinsoft.DDI.Api
{
    [AllowAnonymous, SdrAuthorize]
    [Route("ddi/[action]")]
    [ApiExplorerSettings(GroupName = "DDI")]
    public class DdiService : BaseApiController<DdiBll>, IReceiverController
    {
        #region IReceiverController

        string IReceiverController.SdrCode => this.SdrCode;

        Receiver? IReceiverController.RawReceiver
        {
            get => _receiver;
            set => _receiver = value;
        }

        #endregion IReceiverController

        #region Receiver

        protected virtual string SdrCode => this.Request.Headers[Config.SDR.CodeHeader].AsString();

        private Receiver? _receiver = null;

        protected virtual Receiver CurrentReceiver => _receiver ??= this.GetCurrentReceiver();

        protected virtual Receiver GetCurrentReceiver()
        {
            var sdrCode = this.SdrCode;

            if (sdrCode.IsEmpty())
            {
                throw new Exception("获取经销商授权码失败");
            }

            var repo = this.GetRepo<SysBll>(Config.DefaultCompanyId);

            var receiver = repo.GetEntity<Receiver>(p => p.SdrCode == this.SdrCode);

            if (receiver == null)
            {
                throw new Exception("获取经销商失败");
            }

            return receiver;
        }

        protected override Guid? OperatorCompanyId => this.CurrentReceiver?.CompanyId;

        #endregion Receiver

        #region 测试

        /// <summary>
        /// 加密SDR令牌
        /// </summary>
        [SdrAuthorize(false)]
        [HttpGet]
        public string EncryptSdrToken([FromQuery] string sdrCode, [FromQuery] DateTime? day = null)
        {
            return Config.Debug
                ? SdrAuthHelper.EncryptSdrToken(sdrCode, day)
                : "只允许测试环境调用";
        }

        /// <summary>
        /// 验证SDR令牌
        /// </summary>
        [SdrAuthorize(false)]
        [HttpGet]
        public bool ValdateSdrToken([FromQuery] string sdrCode, [FromQuery] string sdrToken, [FromQuery] DateTime? day = null)
        {
            return SdrAuthHelper.ValdateSdrToken(sdrCode, sdrToken, day);
        }

        /// <summary>
        /// 测试SDR令牌
        /// </summary>
        [HttpGet]
        public ReceiverModel TestSdrToken()
        {
            return this.CurrentReceiver.Map<ReceiverModel>();
        }

        #endregion 测试

        /// <summary>
        /// 校验经销商授权码
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Interface, Operate = "获取经销商配置")]
        public IActionResult VerifyCode([FromForm] IFormCollection ddiClient)
        {
            object? result = null;

            if (this.CurrentReceiver.Client == null)
            {
                result = new VerifyFailedResult()
                {
                    ResHead = new ClientResult()
                    {
                        ResCode = this.CurrentReceiver.Code,
                        ResMsg = "该经销商未配置"
                    }
                };
            }
            else
            {
                var version = ddiClient["version"].ToString();

                result = this.CurrentReceiver.Client.Map<ReceiverCfg>();
            }

            return this.Xml(result);
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        [HttpPost]
        public string SaveData([FromForm] IFormCollection form)
        {
            return string.Empty;
        }

        /// <summary>
        /// 保存日志到服务端
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        [HttpPost]
        public string SaveLog([FromForm] IFormCollection form)
        {
            return string.Empty;
        }
    }
}