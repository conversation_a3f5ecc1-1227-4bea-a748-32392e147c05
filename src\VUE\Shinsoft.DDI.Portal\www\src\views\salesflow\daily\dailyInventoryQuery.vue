<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>流向查询</el-breadcrumb-item>
        <el-breadcrumb-item>日数据查询</el-breadcrumb-item>
        <el-breadcrumb-item>日库存查询</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="8" type="flex">
        <el-col :span="6">
          <el-date-picker
            v-model="filter.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placement="bottom-start"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.businessLocation"
            placeholder="流向商业省份/城市"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.businessName"
            placeholder="流向商业名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.productCascader"
            :options="productOptions"
            :props="cascaderProps"
            placeholder="产品名称/规格"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="dailyInventoryList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" :index="getTableIndex" />
        <el-table-column prop="businessName" label="经销商名称" min-width="180" fixed="left" />
        <el-table-column prop="businessProvince" label="经销商省份" width="120" fixed="left" />
        <el-table-column prop="businessCity" label="经销商城市" width="120" fixed="left" />
        <el-table-column prop="code" label="经销商Code" width="120" />
        <el-table-column prop="productShortName" label="产品名称" width="120" />
        <el-table-column prop="categoryName" label="产品规格" width="120" />
        <el-table-column prop="batchNo" label="批号" width="120" />
        <el-table-column prop="quantity" label="数量" width="100" align="right" />
        <el-table-column prop="isFlowProduct" label="是否流向产品" width="120">
          <template #default="{ row }">
            <span>{{ row.isFlowProduct ? '是' : '否' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="search"
        @current-change="search"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'DailyInventoryQuery',
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        dateRange: [],
        businessLocation: '',
        businessName: '',
        channelLevel: '',
        manager: '',
        productCascader: [],
        batchNo: '',
        isFlowProduct: ''
      },
      // 产品级联选择器配置
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        expandTrigger: 'hover'
      },
      // 产品选项数据
      productOptions: [
        {
          value: 'amoxicillin',
          label: '阿莫西林胶囊',
          children: [
            { value: '0.25g*24', label: '0.25g*24粒' },
            { value: '0.5g*24', label: '0.5g*24粒' }
          ]
        },
        {
          value: 'cephalexin',
          label: '头孢氨苄胶囊',
          children: [
            { value: '0.25g*24', label: '0.25g*24粒' },
            { value: '0.5g*12', label: '0.5g*12粒' }
          ]
        },
        {
          value: 'azithromycin',
          label: '阿奇霉素片',
          children: [
            { value: '0.25g*6', label: '0.25g*6片' },
            { value: '0.5g*3', label: '0.5g*3片' }
          ]
        }
      ],
      totalCount: 0,
      dailyInventoryList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          businessName: '北京康健医药有限公司',
          businessProvince: '北京市',
          businessCity: '北京市',
          code: 'BJ001',
          manager: '张三',
          productShortName: '阿莫西林胶囊',
          categoryName: '0.25g*24粒',
          batchNo: 'B20240120001',
          expiryDate: '2025-12-31',
          quantity: 500,
          channelLevel: 'Tier 1',
          isFlowProduct: true
        },
        {
          id: 2,
          businessName: '上海华润医药',
          businessProvince: '上海市',
          businessCity: '上海市',
          code: 'SH001',
          manager: '李四',
          productShortName: '头孢氨苄胶囊',
          categoryName: '0.5g*12粒',
          batchNo: 'B20240120002',
          expiryDate: '2025-06-30',
          quantity: 200,
          channelLevel: 'Tier 2',
          isFlowProduct: false
        },
        {
          id: 3,
          businessName: '广州南方医药经销商',
          businessProvince: '广东省',
          businessCity: '广州市',
          code: 'GZ001',
          manager: '王五',
          productShortName: '阿奇霉素片',
          categoryName: '0.25g*6片',
          batchNo: 'B20240120003',
          expiryDate: '2026-03-15',
          quantity: 300,
          channelLevel: 'Tier 1',
          isFlowProduct: true
        }
      ]
    }
  },
  mounted() {
    this.search();
  },
  computed: {
    // 获取选中的产品名称
    selectedProductName() {
      if (this.filter.productCascader && this.filter.productCascader.length > 0) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        return productOption ? productOption.label : ''
      }
      return ''
    },
    // 获取选中的产品规格
    selectedProductSpec() {
      if (this.filter.productCascader && this.filter.productCascader.length > 1) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        if (productOption && productOption.children) {
          const specOption = productOption.children.find(item => item.value === this.filter.productCascader[1])
          return specOption ? specOption.label : ''
        }
      }
      return ''
    }
  },
  methods: {
    // 获取表格序号（支持分页）
    getTableIndex(index) {
      return (this.filter.page - 1) * this.filter.per + index + 1;
    },

    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadDailyInventoryList();
    },

    // 加载日库存列表数据
    loadDailyInventoryList() {
      this.loading = true;
      
      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.dailyInventoryList;
        
        // 根据商业名称过滤
        if (this.filter.businessName) {
          filteredList = filteredList.filter(item =>
            item.businessName.includes(this.filter.businessName)
          );
        }
        
        // 根据产品级联选择器过滤
        if (this.filter.productCascader && this.filter.productCascader.length > 0) {
          const productName = this.selectedProductName;
          const productSpec = this.selectedProductSpec;

          filteredList = filteredList.filter(item => {
            let match = true;
            if (productName && item.productShortName) {
              match = match && item.productShortName.includes(productName);
            }
            if (productSpec && item.categoryName) {
              match = match && item.categoryName.includes(productSpec);
            }
            return match;
          });
        }
        
        // 根据批号过滤
        if (this.filter.batchNo) {
          filteredList = filteredList.filter(item =>
            item.batchNo.includes(this.filter.batchNo)
          );
        }
        
        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.dailyInventoryList = filteredList.slice(start, end);
        
        this.loading = false;
      }, 500);
    },
    // 重置查询条件
    reset() {
      this.filter = {
        page: 1,
        per: 10,
        dateRange: [],
        businessLocation: '',
        businessName: '',
        channelLevel: '',
        manager: '',
        productCascader: [],
        batchNo: '',
        isFlowProduct: ''
      };
      this.search();
    },
    // 刷新数据
    refreshData() {
      this.$message.success('数据已刷新');
      this.search();
    },
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },
    // 直销查询
    directSales() {
      this.$message.info('直销查询功能开发中...');
      // 这里可以添加直销相关的查询逻辑
    }
  }
}
</script>


