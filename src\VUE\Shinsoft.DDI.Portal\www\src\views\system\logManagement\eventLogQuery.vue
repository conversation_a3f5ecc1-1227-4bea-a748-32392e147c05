<!--事件日志管理 - 已替换为Element Plus组件，权限控制已临时注释-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>{{
          $t("main.mainSystemManagement")
        }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t("main.systemSetting") }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{
          $t("main.mainEventLogManagement")
        }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="4">
          <el-input v-model="filter.operate" placeholder="操作" clearable />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.userDisplayName"
            placeholder="操作人"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="changeTimeRange"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading"
            >查询</el-button
          >
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 - 预留空白区域 -->
    <div class="action-container">
      <div class="action-buttons"></div>
    </div>
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="dataList"
        stripe
        size="small"
        @sort-change="handleSortChange"
        style="width: 100%"
      >
        <el-table-column
          type="index"
          label="序号"
          width="70"
          align="center"
          :index="indexMethod"
        />
        <el-table-column
          prop="operate"
          label="操作"
          min-width="150"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.operate || "" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="userDisplayName"
          label="操作人"
          min-width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.userDisplayName || "" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="logTime"
          label="日志时间"
          min-width="160"
          align="center"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.logTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="ip"
          label="IP地址"
          min-width="126"
          align="center"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.ip || "" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="duration"
          label="耗时(ms)"
          min-width="100"
          align="center"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.duration || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="查看" placement="top">
              <el-button
                icon="View"
                circle
                size="small"
                @click="handleShowDetail(row.id)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.pageIndex"
        v-model:page-size="filter.pageSize"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>
    <!-- 显示详细信息弹窗 -->
    <el-dialog
      v-model="showDetail"
      title="操作日志详情"
      width="70%"
      :close-on-click-modal="false"
      @close="handleShowDetailClose"
    >
      <el-form :model="detailInfo" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="操作:">
              <el-input v-model="detailInfo.operate" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作人:">
              <el-input v-model="detailInfo.userDisplayName" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日志时间:">
              <el-input v-model="detailInfo.logTime" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="耗时:">
              <el-input v-model="detailInfo.duration" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="IP地址:">
              <el-input v-model="detailInfo.ip" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方法:">
              <el-input v-model="detailInfo.method" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="URL:">
              <el-input v-model="detailInfo.url" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="输入参数:">
              <el-input
                v-model="detailInfo.input"
                type="textarea"
                :rows="6"
                readonly
                resize="none"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="输出结果:">
              <el-input
                v-model="detailInfo.output"
                type="textarea"
                :rows="6"
                readonly
                resize="none"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleShowDetailClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";

export default {
  name: "EventLogQuery",
  data() {
    return {
      loading: false,
      showDetail: false,
      detailInfo: {
        operate: "",
        userDisplayName: "",
        logTime: "",
        duration: "",
        ip: "",
        method: "",
        url: "",
        input: "",
        output: "",
      },
      totalCount: 0,
      timeRange: [], // 时间范围选择器的值
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: "logTime desc",
        category: "API", // 操作日志固定为API类别
        operate: "",
        userDisplayName: "",
        logTime: [],
      },
      dataList: [],
    };
  },
  mounted() {
    // TODO: 原有的权限初始化已临时注释，正式环境需要恢复
    // this.behaviors.behaviorsSession(localStorage.behaviors, 'eventLogQuery');
    this.queryDataList();
  },
  methods: {
    // 时间范围变化处理
    changeTimeRange(value) {
      this.filter.logTime = value || [];
    },

    // 查询
    search() {
      this.filter.pageIndex = 1;
      this.queryDataList();
    },
    // 获取列表数据
    queryDataList() {
      this.loading = true;
      this.$http
        .get("/Log/QueryLog", { params: this.filter })
        .then((response) => {
          if (response.data && response.data.success) {
            // axios拦截器已经处理了嵌套的data结构，直接访问datas即可
            this.dataList = response.data.datas || [];
            this.totalCount = response.data.total || 0;

          } else {
            this.dataList = [];
            this.totalCount = 0;
          }
        })
        .catch((error) => {
          this.dataList = [];
          this.totalCount = 0;
          this.$message.error("查询操作日志失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 改变页面大小
    changePageSize(value) {
      this.filter.pageSize = value;
      this.filter.pageIndex = 1;
      this.queryDataList();
    },
    // 改变页码
    changePage(value) {
      this.filter.pageIndex = value;
      this.queryDataList();
    },
    // 处理排序变化
    handleSortChange({ prop, order }) {
      if (order) {
        const sortOrder = order === "ascending" ? "asc" : "desc";
        this.filter.order = `${prop} ${sortOrder}`;
      } else {
        this.filter.order = "logTime desc"; // 默认排序
      }
      this.queryDataList();
    },
    // 序号计算方法
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1;
    },
    // 格式化日期时间
    formatDateTime(dateTime) {
      return moment(dateTime).format("YYYY-MM-DD HH:mm:ss");
    },
    // 查看详情
    handleShowDetail(id) {
      if (!id) {
        this.$message.error("日志ID不能为空");
        return;
      }

      this.$http
        .get("/Log/GetLog", { params: { id: id } })
        .then((response) => {
          if (response.data && response.data.success) {
            const logDetail = response.data.data;
            this.detailInfo = {
              operate: logDetail.operate,
              userDisplayName: logDetail.userDisplayName,
              logTime: this.formatDateTime(logDetail.logTime),
              duration: `${logDetail.duration || 0}ms`,
              ip: logDetail.ip,
              method: logDetail.method,
              url: logDetail.url,
              input: logDetail.input,
              output: logDetail.output,
            };
            this.showDetail = true;
          } else {
            const message =
              response.data?.Message ||
              response.data?.message ||
              "获取日志详情失败";
            this.$message.error(message);
          }
        })
        .catch((error) => {
          this.$message.error("获取日志详情失败");
        });
    },
    // 关闭详情弹窗
    handleShowDetailClose() {
      this.showDetail = false;
      this.detailInfo = {
        operate: "",
        userDisplayName: "",
        logTime: "",
        duration: "",
        ip: "",
        method: "",
        url: "",
        input: "",
        output: "",
      };
    },
  },
};
</script>

<style scoped>
/* 事件日志查询页面特有样式 */

/* 确保表头所有列都有背景色 */
.el-table thead th {
  background-color: #f8f8f9 !important;
}

.el-table th.el-table__cell {
  background-color: #f8f8f9 !important;
}

/* 表格边框颜色统一 */
.el-table {
  border: 1px solid #dddee1;
}

.el-table th,
.el-table td {
  border-color: #dddee1;
}

/* 表格行高亮样式 */
.el-table tbody tr:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
}

.el-table tbody tr:hover td {
  background-color: rgba(253, 158, 0, 0.1) !important;
}
</style>
