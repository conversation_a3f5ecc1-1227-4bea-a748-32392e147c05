using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using Shinsoft.Core;
using Shinsoft.DDI.Entities;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 药企查询条件
    /// 用于药企列表查询的过滤条件
    /// </summary>
    [Description("药企查询条件")]
    [DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Manufacturer))]
    public partial class ManufacturerFilter : PagingFilterModel
    {
    }

    /// <summary>
    /// 药企选择器查询条件
    /// 用于药企选择器的过滤条件
    /// </summary>
    [Description("药企选择器查询条件")]
    [DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Manufacturer))]
    public partial class ManufacturerSelectorFilter : PagingFilterModel
    {
    }
}