<!--
/**
 * 产品规格管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑产品规格' : '新增产品规格'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="productSpecFormRef"
      :model="productSpecForm"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <!-- 规格编码 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="规格编码" prop="Code">
            <el-input v-model="productSpecForm.Code" placeholder="请输入规格编码" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="isEdit ? 12 : 24">
          <el-form-item label="产品" prop="ProductId">
            <el-cascader
              v-model="cascaderValue"
              :options="manufacturerProductOptions"
              :props="cascaderProps"
              placeholder="请选择药企和产品"
              style="width: 100%"
              @change="handleCascaderChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="规格" prop="Spec">
            <el-input v-model="productSpecForm.Spec" placeholder="请输入规格" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位" prop="Unit">
            <el-input v-model="productSpecForm.Unit" placeholder="请输入单位" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="剂型" prop="DosageFormId">
            <el-select v-model="productSpecForm.DosageFormId" placeholder="请选择剂型" style="width: 100%">
              <el-option
                v-for="dosageForm in dosageFormList"
                :key="dosageForm.id"
                :label="dosageForm.name"
                :value="dosageForm.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="生产厂家" prop="PharmaceuticalFactory">
            <el-input v-model="productSpecForm.PharmaceuticalFactory" placeholder="请输入生产厂家" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分型" prop="MaterialGroup">
            <el-input v-model="productSpecForm.MaterialGroup" placeholder="请输入分型" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'productSpecDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const productSpecFormRef = ref(null)
    
    // 保存加载状态
    const saveLoading = ref(false)
    
    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })
    
    // 表单数据
    const productSpecForm = reactive({
      ProductId: '',
      Code: '',
      Spec: '',
      Unit: '',
      DosageFormId: '',
      PharmaceuticalFactory: '',
      MaterialGroup: ''
    })

    // 级联选择器的值
    const cascaderValue = ref([])

    // 级联选择器配置
    const cascaderProps = {
      value: 'id',
      label: 'name',
      children: 'products',
      checkStrictly: false
    }

    // 药企-产品级联选择器选项
    const manufacturerProductOptions = ref([])

    // 剂型列表
    const dosageFormList = ref([])

    // 表单验证规则 - 动态规则，根据编辑模式调整
    const formRules = computed(() => ({
      ProductId: [
        { required: true, message: '请选择产品', trigger: 'change' }
      ],
      Code: isEdit.value ? [
        { required: true, message: '请输入规格编码', trigger: 'blur' },
        { max: 50, message: '编码长度不能超过50个字符', trigger: 'blur' }
      ] : [],
      Spec: [
        { required: true, message: '请输入规格', trigger: 'blur' },
        { max: 50, message: '规格长度不能超过50个字符', trigger: 'blur' }
      ],
      Unit: [
        { max: 50, message: '单位长度不能超过50个字符', trigger: 'blur' }
      ],
      DosageFormId: [],
      PharmaceuticalFactory: [
        { max: 200, message: '生产厂家长度不能超过200个字符', trigger: 'blur' }
      ],
      MaterialGroup: [
        { max: 50, message: '分型长度不能超过50个字符', trigger: 'blur' }
      ]
    }))

    /**
     * 加载药企-产品级联选择器数据
     */
    const loadManufacturerProductOptions = () => {
      // TODO: 实际项目中需要调用API接口获取药企和产品数据
      // this.$http.get('/Manufacturer/GetManufacturerProductTree').then(response => {
      //   if (response.success) {
      //     manufacturerProductOptions.value = response.data;
      //   } else {
      //     ElMessage.error(response.message || '加载药企产品数据失败');
      //   }
      // });

      // 模拟药企-产品级联数据
      manufacturerProductOptions.value = [
        {
          id: '1',
          name: '拜耳医药保健有限公司',
          products: [
            { id: '1', name: '阿司匹林肠溶片' },
            { id: '2', name: '拜阿司匹林片' }
          ]
        },
        {
          id: '2',
          name: '华北制药集团有限责任公司',
          products: [
            { id: '3', name: '青霉素注射液' },
            { id: '4', name: '头孢克肟颗粒' }
          ]
        },
        {
          id: '3',
          name: '扬子江药业集团有限公司',
          products: [
            { id: '5', name: '布洛芬缓释胶囊' },
            { id: '6', name: '甲硝唑片' }
          ]
        }
      ]
    }

    /**
     * 级联选择器变化处理
     */
    const handleCascaderChange = (value) => {
      if (value && value.length === 2) {
        productSpecForm.ProductId = value[1] // 产品ID
      } else {
        productSpecForm.ProductId = ''
      }
    }

    /**
     * 加载剂型列表
     */
    const loadDosageFormList = () => {
      // TODO: 实际项目中需要调用API接口获取剂型列表
      // this.$http.get('/Dict/GetDosageFormList').then(response => {
      //   if (response.success) {
      //     dosageFormList.value = response.data;
      //   } else {
      //     ElMessage.error(response.message || '加载剂型列表失败');
      //   }
      // });

      // 模拟剂型数据
      dosageFormList.value = [
        { id: '1', name: '片剂' },
        { id: '2', name: '胶囊剂' },
        { id: '3', name: '注射剂' },
        { id: '4', name: '颗粒剂' },
        { id: '5', name: '口服液' },
        { id: '6', name: '软膏剂' }
      ]
    }

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(productSpecForm, {
        ProductId: '',
        Code: '',
        Spec: '',
        Unit: '',
        DosageFormId: '',
        PharmaceuticalFactory: '',
        MaterialGroup: ''
      })

      cascaderValue.value = []

      if (productSpecFormRef.value) {
        productSpecFormRef.value.resetFields()
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = (recordId) => {
      // TODO: 实际项目中需要调用API接口获取数据
      // this.$http.get(`/ProductSpec/GetProductSpec/${recordId}`).then(response => {
      //   if (response.success) {
      //     Object.assign(productSpecForm, response.data);
      //   } else {
      //     ElMessage.error(response.message || '加载数据失败');
      //   }
      // });

      // 模拟数据加载
      console.log('加载产品规格数据，ID:', recordId)
      setTimeout(() => {
        const mockData = {
          ProductId: '1',
          Code: 'PS001',
          Spec: '100mg*30片',
          Unit: '盒',
          DosageFormId: '1',
          PharmaceuticalFactory: '拜耳医药保健有限公司',
          MaterialGroup: '西药'
        }
        Object.assign(productSpecForm, mockData)

        // 设置级联选择器的值（药企ID: 1, 产品ID: 1）
        cascaderValue.value = ['1', '1']
      }, 100)
    }

    /**
     * 保存产品规格信息
     */
    const handleSave = () => {
      productSpecFormRef.value.validate((valid) => {
        if (valid) {
          saveLoading.value = true

          // TODO: 实际项目中需要调用API接口
          // const apiUrl = isEdit.value ? '/ProductSpec/UpdateProductSpec' : '/ProductSpec/CreateProductSpec';
          // this.$http.post(apiUrl, productSpecForm).then(response => {
          //   if (response.success) {
          //     ElMessage.success(isEdit.value ? '产品规格信息更新成功' : '产品规格添加成功');
          //     emit('success');
          //     handleClose();
          //   } else {
          //     ElMessage.error(response.message || '操作失败');
          //   }
          //   saveLoading.value = false;
          // }).catch(error => {
          //   ElMessage.error('操作失败：' + error.message);
          //   saveLoading.value = false;
          // });

          // 模拟保存操作
          setTimeout(() => {
            ElMessage.success(isEdit.value ? '产品规格信息更新成功' : '产品规格添加成功')
            saveLoading.value = false
            emit('success')
            handleClose()
          }, 1000)
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      }
    })

    // 组件挂载时加载下拉列表数据
    onMounted(() => {
      loadManufacturerProductOptions()
      loadDosageFormList()
    })

    return {
      productSpecFormRef,
      saveLoading,
      isEdit,
      dialogVisible,
      productSpecForm,
      formRules,
      cascaderValue,
      cascaderProps,
      manufacturerProductOptions,
      dosageFormList,
      handleCascaderChange,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>
