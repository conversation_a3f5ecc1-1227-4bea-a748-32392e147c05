<template>
  <div class="test-api-container">
    <h2>API测试页面</h2>
    
    <div class="test-section">
      <h3>测试QueryReceiverClient接口</h3>
      <el-button @click="testQueryApi" :loading="loading">测试查询接口</el-button>
      
      <div v-if="result" class="result-section">
        <h4>返回结果：</h4>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
      
      <div v-if="error" class="error-section">
        <h4>错误信息：</h4>
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { configApi } from '@/api/configApi'

export default {
  name: 'TestApi',
  data() {
    return {
      loading: false,
      result: null,
      error: null
    }
  },
  methods: {
    async testQueryApi() {
      this.loading = true;
      this.result = null;
      this.error = null;
      
      try {
        const params = {
          pageIndex: 1,
          pageSize: 10,
          order: 'ID desc'
        };
        
        const response = await configApi.queryReceiverClient(params);
        this.result = response.data;
        
        this.$message.success('API调用成功');
      } catch (error) {
        this.error = error.message || error.toString();
        this.$message.error('API调用失败');
        console.error('API调用失败:', error);
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
.test-api-container {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.result-section, .error-section {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
}

.result-section {
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
}

.error-section {
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
