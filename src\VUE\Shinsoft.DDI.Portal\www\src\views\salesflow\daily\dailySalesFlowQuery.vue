<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>流向查询</el-breadcrumb-item>
        <el-breadcrumb-item>日数据查询</el-breadcrumb-item>
        <el-breadcrumb-item>日流向查询</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <!-- 第一行 -->
      <el-row :gutter="8" type="flex">
        <el-col :span="4">
          <el-date-picker
            v-model="filter.queryDate"
            type="month"
            placeholder="销售月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            placement="bottom-start"
            popper-class="date-picker-dropdown"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="filter.salesDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placement="bottom-start"
            style="width: 100%"
          />
        </el-col>
         <el-col :span="4">
          <el-cascader
            v-model="filter.senderProvinceAndCity"
            :options="cityList"
            placeholder="发货方省份/城市"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.distributorCode"
            placeholder="发货方Code"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.distributorName"
            placeholder="发货方名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.receiverProvinceAndCity"
            :options="cityList"
            placeholder="收货方省份/城市"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.receiverCode"
            placeholder="收货方Code"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.receiverName"
            placeholder="收货方名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.productCascader"
            :options="productOptions"
            :props="cascaderProps"
            placeholder="产品名称/规格"
            clearable
            filterable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="handleSearch">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        border
        stripe
        size="small"
        v-loading="loading"
        element-loading-text="数据加载中..."
        style="width: 100%"
      >

        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" :index="getTableIndex" />
        <el-table-column prop="salesDate" label="销售日期" width="100" align="center" fixed="left" />
        <el-table-column prop="senderName" label="发货方名称" width="200" align="left" show-overflow-tooltip fixed="left" />
        <el-table-column prop="receiverName" label="收货方名称" width="200" align="left" show-overflow-tooltip fixed="left" />
        <el-table-column prop="productName" label="产品名称" width="100" align="center" show-overflow-tooltip fixed="left" />
        <el-table-column prop="productSpec" label="产品规格" width="110" align="center" fixed="left" />
        <el-table-column prop="queryDate" label="记录月份" width="100" align="center" />
        <el-table-column prop="senderProvince" label="发货方省份" width="120" align="center" />
        <el-table-column prop="senderCity" label="发货方城市" width="120" align="center" />
        <el-table-column prop="senderCode" label="发货方Code" width="150" align="center" />
        <el-table-column prop="receiverProvince" label="收货方省份" width="120" align="center" />
        <el-table-column prop="receiverCity" label="收货方城市" width="120" align="center" />
        <el-table-column prop="receiverCode" label="收货方Code" width="150" align="center" />
        <el-table-column prop="productBatch" label="批号" width="120" align="center" />
        <el-table-column prop="quantity" label="数量" width="100" align="center" />
        <el-table-column prop="orderNumber" label="订单号" width="130" align="center" />
        <el-table-column prop="remark" label="备注" width="150" align="center" show-overflow-tooltip />
      </el-table>
    </div>
    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'DailySalesFlowQuery',
  data() {
    return {
      loading: false,
      filter: {
        queryDate: '',
        salesDateRange: [],
        senderProvinceAndCity: [],
        distributorCode: '',
        distributorName: '',
        receiverProvinceAndCity: [],
        receiverCode: '',
        receiverName: '',
        productCode: '',
        productCascader: [], // 产品级联选择器值
        productUnit: '',
        productBatch: '',
        productionDate: '',
        expiryDate: '',
        quantity: '',
        unitPrice: '',
        totalAmount: '',
        invoiceNumber: '',
        invoiceDate: '',
        orderNumber: '',
        deliveryNumber: '',
        remark: '',
        dataSource: '',
        uploadTime: '',
        processStatus: '',
        errorMessage: ''
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 产品级联选择器配置
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        expandTrigger: 'hover'
      },
      // 产品选项数据
      productOptions: [
        {
          value: 'amoxicillin',
          label: '阿莫西林胶囊',
          children: [
            { value: '0.25g*24', label: '0.25g*24粒' },
            { value: '0.5g*12', label: '0.5g*12粒' },
            { value: '0.25g*48', label: '0.25g*48粒' }
          ]
        },
        {
          value: 'cephalexin',
          label: '头孢氨苄胶囊',
          children: [
            { value: '0.25g*20', label: '0.25g*20粒' },
            { value: '0.5g*24', label: '0.5g*24粒' }
          ]
        },
        {
          value: 'aspirin',
          label: '阿司匹林肠溶片',
          children: [
            { value: '25mg*30', label: '25mg*30片' },
            { value: '100mg*30', label: '100mg*30片' }
          ]
        }
      ],
      // 城市级联数据
      cityList: [
        {
          value: 'beijing',
          label: '北京市',
          children: [
            { value: 'dongcheng', label: '东城区' },
            { value: 'xicheng', label: '西城区' },
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'fengtai', label: '丰台区' }
          ]
        },
        {
          value: 'shanghai',
          label: '上海市',
          children: [
            { value: 'huangpu', label: '黄浦区' },
            { value: 'xuhui', label: '徐汇区' },
            { value: 'changning', label: '长宁区' },
            { value: 'jingan', label: '静安区' }
          ]
        },
        {
          value: 'guangdong',
          label: '广东省',
          children: [
            { value: 'guangzhou', label: '广州市' },
            { value: 'shenzhen', label: '深圳市' },
            { value: 'dongguan', label: '东莞市' },
            { value: 'foshan', label: '佛山市' }
          ]
        }
      ]
    }
  },
  computed: {
    // 获取选中的产品名称
    selectedProductName() {
      if (this.filter.productCascader && this.filter.productCascader.length > 0) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        return productOption ? productOption.label : ''
      }
      return ''
    },
    // 获取选中的产品规格
    selectedProductSpec() {
      if (this.filter.productCascader && this.filter.productCascader.length > 1) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        if (productOption && productOption.children) {
          const specOption = productOption.children.find(item => item.value === this.filter.productCascader[1])
          return specOption ? specOption.label : ''
        }
      }
      return ''
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 计算表格序号（考虑分页）
    getTableIndex(index) {
      return (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
    },

    // 加载数据
    loadData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.tableData = this.generateMockData()
        this.pagination.total = 100
        this.loading = false
      }, 1000)
    },
    
    // 生成模拟数据
    generateMockData() {
      const data = []
      for (let i = 1; i <= this.pagination.pageSize; i++) {
        data.push({
          id: i,
          queryDate: '2024-01',
          salesDate: '2024-01-15',
          senderProvince: '广东省',
          senderCity: '深圳市',
          senderCode: 'GD001',
          senderName: '深圳市某某医药有限公司',
          receiverProvince: '广东省',
          receiverCity: '广州市',
          receiverCode: 'GD002',
          receiverName: '广州市某某药店',
          productCode: 'P001',
          productName: '阿莫西林胶囊',
          productSpec: '0.25g*24粒',
          productUnit: '盒',
          productBatch: 'B20240115',
          productionDate: '2024-01-15',
          expiryDate: '2026-01-14',
          quantity: 100,
          unitPrice: 15.50,
          totalAmount: 1550.00,
          invoiceNumber: 'INV20240115001',
          invoiceDate: '2024-01-15',
          orderNumber: 'ORD20240115001',
          deliveryNumber: 'DEL20240115001',
          remark: '正常销售',
          dataSource: '系统上传',
          uploadTime: '2024-01-15 10:30:00',
          processStatus: '已处理',
          errorMessage: ''
        })
      }
      return data
    },
    
    // 查询
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },

    // 导出数据
    handleExport() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      this.$message.info('导出功能开发中...')
      // TODO: 实现实际的导出功能
      // const exportParams = {
      //   ...this.searchForm,
      //   // 添加其他需要的参数
      // }
      // this.$http.post('/SalesFlow/ExportDailySalesFlow', exportParams, {
      //   responseType: 'arraybuffer'
      // }).then(response => {
      //   const fileDownload = require('js-file-download')
      //   const fileName = `日流向数据_${new Date().toISOString().slice(0, 10)}.xlsx`
      //   fileDownload(response.data, fileName)
      //   this.$message.success('导出成功')
      // }).catch(error => {
      //   this.$message.error('导出失败')
      // })
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    }
  }
}
</script>
