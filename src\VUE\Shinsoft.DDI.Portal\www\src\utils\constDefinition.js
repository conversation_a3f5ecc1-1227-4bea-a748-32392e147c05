/* eslint-disable comma-spacing */
/* eslint-disable key-spacing */
/* eslint-disable spaced-comment */
const constDefinition = {
    paymentPlanForComparison: {
        addNumbers: 1,
        differenceNumbers: 2,
        deleteNumbers: 3
    },
    whetherSpecialNeedsList: {
        TRUE: 'true',
        FALSE: 'false',
        YES: '是特殊需求',
        NO: '不是特殊需求',
        OPEN: 'open',
        OPENYES: '是',
        CLOSE: 'close',
        CLOSENO: '否'
    },
    whetherClassificationCodingList: {
        TRUE: 'true',
        FALSE: 'false',
        YES: '带税收分类编码',
        NO: '不带税收分类编码',
        OPEN: 'open',
        OPENYES: '是',
        CLOSE: 'close',
        CLOSENO: '否'
    },
    displayExpiredValue: {
        displayExpiredTrue: 'true',
        displayExpiredFalse: 'false'
    },
    displayExpired: {
        displayExpiredYes: '不隐藏过期数据',
        displayExpiredNo: '隐藏已过期数据'
    },
    selectPriceType: {
        ACTUALPRICE: '实际采购价',
        BIDDINGNETPRICE: '执行中标/挂网价'
    },
    selectPriceTypeValue: {
        ACTUALPRICEOne: '1',
        BIDDINGNETPRICETWO: '2'
    },
    whetherAbnormal: {
        abnormal: '异常',
        normal: '正常',
        abnormalTrue: 'true',
        normalFalse: 'false'
    },
    mailIsFailed: {
        fail: '失败',
        success: '成功',
        failTrue: 'true',
        successFalse: 'false'
    },
    whetherDisable: {
        enable: '启用',
        enableFalse: 'false',
        discontinueUse: '停用',
        discontinueUseTrue: 'true'
    },
    whetherStatus: {
        enable: '启用',
        enableFalse: '1',
        discontinueUse: '停用',
        discontinueUseTrue: '2'
    },
    compensationSummaryList: {
        militarySupplyStationValue: 32,
        provinceBiddingLabel: '省级标',
        provinceBiddingTabs: '1',
        cityBiddingLabel: '市级标',
        cityBiddingTabs: '2',
        militarySupplyStationLabel: '军供站',
        militarySupplyStationTabs: '32',
        singleHospitalLabel: '单家医院',
        singleHospitalTabs: '16',
        singleTerminalLabel: '单家终端',
        singleTerminalTabs: '8',
        countyBiddingLabel: '县级标',
        countyBiddingTabs: '4'
    },
    compensationCertificateType: {
        returnCertificate: 1,
        collectPaymentCertificate: 2,
        collectPaymentCertificateDetail: 3
    },
    compensationGenerationStatus:
        {
            NotUploaded: 10,
            Uploaded: 20,
            Calculated: 30,
            Confirmed: 40,
            Generated: 50,
            Sealing: 60,
            Sealed: 70,
            Release: 80
        },
    yesOrDeny: {
        yes: '是',
        deny: '否'
    },
    yesOrDenyValue: {
        yes: 'true',
        deny: 'false'
    },
    cdmsProductUrl: {
        domain: 'cdms.sperogenix.com',
        ip: '***************'
    },
    employeeStatus: {
        INSERVICE: '在职',
        LEAVE: '离职'
    },
    employeeStatusValue: {
        INSERVICEOne: 1,
        LEAVETWO: 2
    },
    loginStatus: {
        LOGINSUCCESSFUL: '登录成功',
        LOGINFAILED: '登录失败'
    },
    loginStatusValue: {
        LOGINSUCCESSFULZERO: 0,
        LOGINFAILEDONE: 1
    },
    biddingProject: {
        upload: {
            ISTYPEONE: 1,
            ISTYPETWO: 2,
            ISTYPETHREE: 3
        },
        isRequireCity: {
            ISREQUIRECITYONE: 1,
            ISREQUIRECITYTWO: 2,
            ISREQUIRECITYTHREE: 3,
            ISREQUIRECITYFOUR: 4
        }
    },
    dictionary: {
        PROJECTSCOPE: 'BiddingProjectScope', //项目执行范围
        PROVINCEBIDDING: 'ProvinceBidding', //省级标
        CITYBIDDING: 'CityBidding', //市级标
        COUNTYBIDDING: 'CountyBidding', //区县标
        CITYMARK: '地市标',
        DRUGSTORETYPE: 'DrugStoreType', // 药房类型
        DRUGSTORELEVEL: 'DrugStoreLevel', // 药店分级
        SALESAREA: 'SalesArea' // 销售大区
    },
    doctor: {
        doctorLevel: 'DoctorClass',
        speakerLevel: 'SpeakerClass',
        doctorTitleCode: 'DoctorTitle',
        doctorPositionCode: 'DoctorPosition',
        academicPositions: 'AcademicPositions',
        academicTitle: 'AcademicTitle',
        speechAbility: 'SpeechAbility',
        Education: 'Education',
        doctorStage: 'DoctorStage',
        enumEntityStatus: {
            normal: 1, // 正常
            locked: 2, // 已锁定
            delete: 3 // 已删除
        }
    },
    salesFlow: {
        complaintsFormStatus: {
            none: 0,
            request: 10,
            confirm: 30,
            reject: 40,
            cancel: 50
        }
    },
    receiver: {
        enumFormStatus: {
            none: 0, // 无
            draft: 10, // 草稿
            waitingEffect: 15, // 待生效
            waitingApproval: 20, // 待审批
            waitingConfirm: 30, // 待确认
            waittingDirectorConfirm: 40, // 待总监确认
            completion: 100 // 完成
        },
        tradeType: {
            none: 0, // 无
            sale: 10, // 纯销
            transfer: 20, // 调拨
            other: 30 // 其他
        },
        distributorType: {
            none: 0, // 无
            distributor: 1, // 流向商业
            preparatoryDistributor: 2, // 可追溯商业
            receiver: 3 // 收货方商业
        },
        formType: {
            none: 0,
            upgrade: 1, // 升级流向商业
            downgrade: 2, // 取消
            productChannelAdd: 3, // 新增产品渠道
            changeDistributor: 4, // 流向商业信息变更
            productChannelUpdate: 5, // 变更产品渠道
            productChannelDelete: 6, // 停用产品渠道
            changeReceiver: 7, // 收货方变更
            addDistributor: 8,//新增流向商业
            changeDistributorContractor: 9,//变更流向商业联系人
            addDoctor: 10,
            addSpeaker: 11,
            upgradeSpeaker: 12,
            changeDoctor: 13, //变更医生
            addReceiver: 14, //新增机构
            changeSpeaker: 15 //变更讲者
        },
        dictionary: {
            DOSAGEFORM: 'DosageForm', // 剂型
            DUTY: 'Duty', // 业务领域
            MEDICINEGROUP: 'MedicineGroup', // 医药集团
            BUSINESSMODE: 'BusinessMode', // 经营方式
            BusinessCategory: 'BusinessCategory', // 业务分类
            Outreach: 'Outreach', // 推广方式
            HospitalGrade: 'HospitalGrade', // 医院级别
            HospitalLevel: 'HospitalLevel', // 医院等次
            BUSINESSSTATUS: 'BusinessStatus', // 经营状态
            TIERTYPE: 'TierType', // 流向商业管理级别
            DOWNSTREAMPROPERTY: 'DownstreamProperty', // 性质名称
            RECEIVERPRODUCTCHANNELCHANGEREQUESTEDITTYPEADD: '1', // 渠道变更申请类型
            RECEIVERPRODUCTCHANNELCHANGEREQUESTEDITTYPEMODIFY: '2', // 渠道变更申请类型
            RECEIVERPRODUCTCHANNELCHANGEREQUESTEDITTYPEDELETE: '3', // 渠道变更申请类型
            LICENSETYPE: 'LicenseType', // 证照类型  （目前没有地方使用）
            EXPIRINGLICENSETYPE: 'ExpiringLicenseType', // 临期资质类型
            AUTHORIZATION: 'SperogenixAuthentication ' + localStorage.token, // （目前没有地方使用）
            dictionaryItem: {
                productTypeInstrumentName: '器械'
            },
            PROJECTTYPE: 'ProjectType', //项目类型
            PROJECTSTATUS: 'ProjectStatus', //项目状态
            BIDDINGTYPE: 'BiddingType', //招标类型
            BIDDINGSTATUS: 'BiddingStatus', //招标类型
            PROJECTSCOPE: 'BiddingProjectScope', //项目执行范围
            PROVINCEBIDDING: 'ProvinceBidding', //项目执行范围
            ANNOUNCEMENT: 'Announcement', //方案公布
            MEDICALINSURANCE: 'MedicalInsurance', //医保
            DDISTATUS: 'DDIStatus', //DDI状态
            HOSPITALCLASS: 'HospitalClass', //医院级别
            HOSPITALCLASSWITHOUTCOE: 'HospitalClassWithoutCOE', //医院级别不含COE
            STRATEGHOSPITAL: 'StrategyHospital', //战略医院
            HCOLEVEL: 'HCOLevel', //HCOLevel
            SITE: 'Site', //Site
            DoctorClass: 'DoctorClass',
            SpeakerClass: 'SpeakerClass',
            ImportantCustomerType: 'ImportantCustomerType'
        },
        importMode: {
            none: 0, // 无
            auto: 1, // 自动同步
            manual: 2 // 手工上传
        },
        businessType: {
            salesFlowDaily: 10,
            salesFlowMonthly: 15,
            purchaseDaily: 20,
            purchaseMonthly: 25,
            inventoryDaily: 30
        },
        contractorDuty: {
            invoiceContractor: 'InvoiceContractor', // 发票联系人
            pruchaseContractor: 'PruchaseContractor', // 采购授权人
            sendeeContractor: 'SendeeContractor', // 资料接收人
            ddiContractor: 'DDIContractor', // DDI联系人
            checkBillContractor: 'CheckBillContractor' // 对账单联系人
        },
        enumEntityStatus: {
            normal: 1, // 正常
            locked: 2, // 已锁定
            delete: 3 // 已删除
        },
        enumDistributorType: {
            distributor: 1, // 流向商业
            preparatoryDistributor: 2, // 可追溯商业
            receiver: 3 // 收货方商业
        },
        enumDataStateType: {
            DailyStatus: 60, // 经销商日数据提供情况
            MonthlyStatus: 70, // 经销商月数据提供情况
            SAPSalesFlowDaily: 50, // SAP日数据
            InventoryDaily: 30, // 日库存
            PurchaseMonthly: 25, // 月采购
            PurchaseDaily: 20, // 日采购
            SalesFlowMonthly: 15, // 月流向
            SalesFlowDaily: 10 // 日流向
        },
        otherTypes: {
            InventoryPurchaseOtherType: 'InventoryPurchaseOtherType', // 进货其它类型
            InventorySaleOtherType: 'InventorySaleOtherType' // 销售其它类型
        },
        changeDistributorOperate: { //流向商业流向确认发布、冻结按钮切换
            allowRelease: 0,
            allowFrozen: 1
        },
        channelType: {
            productChannel: 1, //产品渠道
            materialGroupChannel: 2 //分型渠道
        },
        drugStoreExtensionSelectList: [
            {
                value: 'true',
                label: '是'
            },
            {
                value: 'false',
                label: '否'
        }]
    },
    roleCode: {
        admin: 'Admin',
        order: 'Order',
        customerService: 'CustomerService',
        bidding: 'Bidding',
        planning: 'Planning',
        Compensation: 'Compensation'
    },
    employeeType: {
        admin: 1, // 管理员
        employee: 2 // 员工
    },
    enumEntityStatus: {
        normal: 1, // 正常
        locked: 2, // 已锁定
        delete: 3 // 已删除
    },
    enumSalesPlanStatus: {
        waitingForConfirm: 1, // 待确认
        confirmed: 2, // 已确认
        effective: 3, // 已生效
        draft: 4, // 草稿
        rejected: 5, //已拒绝
        waitingApproval: 6 //待审批
    },
    inventory: {
        statisticalMethod: {
            statisticalProduct: 1, // 按产品统计
            statisticalMaterialGroup: 2 // 按分型统计
        },
        whetherLevelingList: {
            noLeveling: 1, // 未调平
            yesLeveling: 2 // 已调平
        },
        feedbackCode: 'InventoryFeedBack'
    },
    report: {
        reportType: {
            reportAll: 1, //总计
            reportMonth: 2 //按月份统计
        }
    },
    pageSizeOpts: [10, 20, 50, 100],
    flowReversal: '流向冲销',
    orderManageMent: {
        orderProcessing: {
            landTransport: 1, //陆运
            airTransport: 2 //空运
        },
        orderRequestStatus: {
            waitingForProcess: 1, //待处理
            processed: 2, //已处理
            cancelled: 3 //已取消
        },
        contractApprovedStatus: {
            WaitingForApproval: 1,
            Approved: 2,
            Refuse: 3
        },
        orderStatus: {
            contractSent: 10, //已发送
            placeAnOrder: 30, //已下单
            stockUp: 35,
            shipped: 40, //已发货
            cancelled: 60 //已取消
        },
        contractStatus: {
            none: 0,
            hasBeenSent: 10,
            waitingForReview: 20,
            reviewed: 30,
            partyAHasSealed: 40,
            partyBHasSealed: 50,
            canceled: 60,
            partyBHasSealedAndCanceled: 70,
            paperContractSentBack: 80,
            autoSignFailed: 90
        },
        contractType: {
            electronic: 1,
            paper: 2
        }
    },
    uploadSize: {
        uploadSizeG3: 3072,
        uploadSizeG5: 5120,
        uploadSizeM20: 20480,
        uploadSizeM10: 10240,
        uploadSizeK200: 200
    },
    feedbackCode: 'ClinicalTrial', // 兴奋期反馈code
    location: {
        province: 1,
        militaryRegion: 2
    },
    biddingNetPrice: {
        hangingnet: '挂网',
        losebid: '未中标',
        notender: '未投标',
        projectStatusCode: 'ProjectStatus',
        announcement: 'Announcement',
        completedNotExecuted: 'CompletedNotExecuted',
        completedExecuted: 'CompletedExecuted',
        end: 'End',
        bidding: 'Bidding',
        BIDDINGNOTBID: 'BiddingNotBid', // 未投标
        NOTCOMPLETEDLOSEBID: 'NotCompletedLoseBid', // 未中标
        NOTCOMPLETEDNOTBID: 'NotCompletedNotBid', // 未投标
        COMPLETEDNOTBID: 'CompletedNotBid', // 未投标
        COMPLETEDLOSEBID: 'CompletedLoseBid' // 未中标
    },
    //疑似调拨数据来源
    salesFlowCategory: {
        daily: 1, //日流向
        monthly: 2 //月流向
    },
    //批号月库存数据来源
    inventoryDataSource: {
        onpassage: 1, //在途
        DDIInventory: 2 //DDI日库存
    },
    message: {
        durationSuccess: 3, //成功消息3秒
        durationError: 5 //错误信息5秒
    },
    //是否int
    isTrue: {
        yes: 1, //是
        no: 0 //否
    },
    targetHospitalwarningType: {
        redWarning: 1, //红色预警
        yellowWarning: 2 //黄色预警
    },
    flowFeedback: {
        onTheWay: 'OnTheWay'
    },
    targetHospitalWarning: {
        isShowAllTargetHospital: false
    },
    tierType1Code: '131',
    addOrder: {
        REMAINPLANQUANTITY: '无计划'
    },
    biddingStep: {
        ENUMENTITYSTATUSONE: 1,
        ENUMENTITYSTATUSTWO: 2
    },
    biddingType: {
        ENUMENTITYSTATUSONE: 1,
        ENUMENTITYSTATUSTWO: 2
    },
    // 表格高度
    TableHeight: {
        TABLEHEIGHT: 450
    },
    productRequirementType: {
        None: 0,
        Specified: 1,
        All: 2
    },
    compensationGenerateFileStatus: {
        None: 0,
        NotGeneration: 1,
        WaitGeneration: 2,
        InGeneration: 3,
        Completed: 4,
        Exception: 5
    },
    receiverTypeGroup: {
        MedicalInstitution: 'MedicalInstitution',
        Distributor: 'Distributor',
        DrugStore: 'DrugStore'
    },
    biddingProjectStatus: {
        Bidding: 'Bidding',
        CompletedExecuted: 'CompletedExecuted',
        End: 'End'
    },
    biddingPriceStatus: {
        StopPurchase: 'StopPurchase', // 停止采购
        BiddingStatusEnd: 'End', // 项目结束
        StopProduction: 'StopProduction', // 停产
        QuotedPrice: 'QuotedPrice', // 报价中
        WinBid: 'WinBid', //已中标
        Purchasing: 'Purchasing' // 采购中
    },
    officialSealStatus: {
        WaitingForApproval: 1,
        Approved: 2
    },
    authenticationStatus: {
        //未认证
        NotCertified: 1,
        // 待审核
        WaitApproval: 2,
        // 已通过初审
        FirstTrial: 3,
        // 未通过审核
        Reject: 4,
        // 已认证
        Certified: 5
    },
    certificateCancelStatus: {
        NoFiles: 1,
        // 正常
        Normal: 2,
        // 已作废
        Canceled: 3,
        // 已更新
        HasUpdated: 4
    },
    invoiceNoticeStatus: {
        //无
        None: 0,
        //待审核
        WaitApproval: 1,
        //审核通过
        Approved: 2
    },
    receiverSignStatus: {
        // 无文件
        NoFiles: 1,
        // 已签章
        Succeeded: 2,
        // 签章失败
        Failed: 3
    },
    quillEditor: {
        customerNoticeMaxSize: 3000000
    },
    accruedExpenseReportTab: {
        byDistributorLabel: '月度预提报告',
        byDistributorTabs: '1',
        byProductLabel: '产品金额汇总',
        byProductTabs: '2'
    },
    accruedExpenseStatus: {
        NotCalculated: 10, // 未计算
        CalculatingUnitPrice: 20, // 正在计算补偿单价
        ExceptionOnUnitPrice: 25, // 计算单价失败
        CalculatedUnitPrice: 30, // 已计算补偿单价
        CalculatingAccruedExpense: 40, // 正在计算月度预提
        ExceptionOnAccruedExpense: 45, // 计算月度预提失败
        CalculatedAccruedExpense: 50, // 已计算月度预提
        Exception: 60 // 计算异常
    },
    quarterCompensationStatus: {
        Calculating: 10,//计算中
        Calculated: 20,//已计算
        Failed: 30,//计算失败
        Exception: 90 //计算异常
    },
    visit: {
        entityStatus: {
            Normal: 1
        },
        visitPlanReleaseStatus: {
            UnRelease: 1
        },
        receiverTypes: {
            DrugStore: 2
        },
        questionGroup: {
            Prescription: 1,
            Competitive: 2,
            Store: 3,
            ColdChain: 4
        },
        questionType: {
            Choice: 1,
            Input: 2,
            Attachment: 3,
            Competitor: 4,
            Text: 5
        },
        additionalFlag: {
            None: 0
        }
    }
}
export default constDefinition
