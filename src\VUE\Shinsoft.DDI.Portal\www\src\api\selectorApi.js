import axios from '@/utils/axios'

/**
 * 选择器相关API
 */
export const selectorApi = {
  /**
   * 获取字典选项
   * @param {string} parentCode 父级字典编码
   * @returns {Promise} API响应，返回字典选项列表
   */
  getDicts(parentCode) {
    return axios.get('/Selector/GetDicts', {
      params: { parentCode }
    })
  },

  /**
   * 获取枚举选项
   * @param {Object} filter 枚举过滤条件
   * @returns {Promise} API响应，返回枚举选项列表
   */
  getEnumInfos(filter = {}) {
    return axios.get('/Selector/GetEnumInfos', {
      params: filter
    })
  }
}

export default selectorApi