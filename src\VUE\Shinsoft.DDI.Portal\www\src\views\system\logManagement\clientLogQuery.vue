<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>系统管理</el-breadcrumb-item>
        <el-breadcrumb-item>日志管理</el-breadcrumb-item>
        <el-breadcrumb-item>客户端日志查询</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="4">
          <el-input v-model="filter.clientIp" placeholder="客户端IP" clearable />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.logContent"
            placeholder="日志内容"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="changeTimeRange"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading"
            >查询</el-button
          >
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 - 预留空白区域 -->
    <div class="action-container">
      <div class="action-buttons"></div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="dataList"
        stripe
        size="small"
        @sort-change="handleSortChange"
        style="width: 100%"
      >
        <el-table-column
          type="index"
          label="序号"
          width="70"
          align="center"
          :index="indexMethod"
        />
        <el-table-column
          prop="clientIp"
          label="客户端IP"
          min-width="140"
          align="center"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.clientIp || "" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="logContent"
          label="日志"
          min-width="300"
          sortable="custom"
        >
          <template #default="{ row }">
            <div class="log-content">
              {{ row.logContent || "" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="clientTime"
          label="客户端时间"
          min-width="160"
          align="center"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.clientTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="serverTime"
          label="服务器时间"
          min-width="160"
          align="center"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.serverTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="查看" placement="top">
              <el-button
                icon="View"
                circle
                size="small"
                @click="handleShowDetail(row.id)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.pageIndex"
        v-model:page-size="filter.pageSize"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 显示详细信息弹窗 -->
    <el-dialog
      v-model="showDetail"
      title="客户端日志详情"
      width="70%"
      :close-on-click-modal="false"
      @close="handleShowDetailClose"
    >
      <el-form :model="detailInfo" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户端IP:">
              <el-input v-model="detailInfo.clientIp" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户端时间:">
              <el-input v-model="detailInfo.clientTime" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务器时间:">
              <el-input v-model="detailInfo.serverTime" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户代理:">
              <el-input v-model="detailInfo.userAgent" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="日志内容:">
              <el-input
                v-model="detailInfo.logContent"
                type="textarea"
                :rows="6"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="详细信息:">
              <el-input
                v-model="detailInfo.details"
                type="textarea"
                :rows="4"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleShowDetailClose">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: "ClientLogQuery",
  data() {
    return {
      loading: false,
      showDetail: false,
      detailInfo: {
        clientIp: "",
        logContent: "",
        clientTime: "",
        serverTime: "",
        userAgent: "",
        details: "",
      },
      totalCount: 0,
      timeRange: [], // 时间范围选择器的值
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: "serverTime desc",
        clientIp: "",
        logContent: "",
        timeRange: [],
      },
      dataList: [],
    };
  },
  mounted() {
    this.queryDataList();
  },
  methods: {
    // 时间范围变化处理
    changeTimeRange(value) {
      this.filter.timeRange = value || [];
    },

    // 查询
    search() {
      this.filter.pageIndex = 1;
      this.queryDataList();
    },

    // 获取列表数据
    queryDataList() {
      this.loading = true;

      // 模拟API调用 - 实际使用时需要替换为真实的API
      setTimeout(() => {
        // 模拟数据
        const mockData = this.generateMockData();
        let filteredData = mockData;

        // 根据客户端IP过滤
        if (this.filter.clientIp) {
          filteredData = filteredData.filter(item =>
            item.clientIp.includes(this.filter.clientIp)
          );
        }

        // 根据日志内容过滤
        if (this.filter.logContent) {
          filteredData = filteredData.filter(item =>
            item.logContent.includes(this.filter.logContent)
          );
        }

        // 根据时间范围过滤
        if (this.filter.timeRange && this.filter.timeRange.length === 2) {
          const startTime = new Date(this.filter.timeRange[0]);
          const endTime = new Date(this.filter.timeRange[1]);
          filteredData = filteredData.filter(item => {
            const serverTime = new Date(item.serverTime);
            return serverTime >= startTime && serverTime <= endTime;
          });
        }

        this.totalCount = filteredData.length;
        this.dataList = filteredData.slice(
          (this.filter.pageIndex - 1) * this.filter.pageSize,
          this.filter.pageIndex * this.filter.pageSize
        );
        this.loading = false;
      }, 500);

      // 真实API调用示例（注释掉的代码）
      /*
      this.$http
        .get("/ClientLog/QueryClientLog", { params: this.filter })
        .then((response) => {
          if (response.data && response.data.success) {
            this.dataList = response.data.datas || [];
            this.totalCount = response.data.total || 0;
          } else {
            this.dataList = [];
            this.totalCount = 0;
          }
        })
        .catch((error) => {
          this.dataList = [];
          this.totalCount = 0;
          this.$message.error("查询客户端日志失败");
        })
        .finally(() => {
          this.loading = false;
        });
      */
    },

    // 生成模拟数据
    generateMockData() {
      const mockData = [];
      const ips = ["*************", "*************", "*********", "***********", "************"];
      const logTypes = [
        "用户登录成功",
        "数据查询操作",
        "文件上传完成",
        "系统配置更新",
        "用户退出登录",
        "数据导出操作",
        "权限验证失败",
        "API调用异常"
      ];

      for (let i = 1; i <= 50; i++) {
        const now = new Date();
        const clientTime = new Date(now.getTime() - Math.random() * 24 * 60 * 60 * 1000);
        const serverTime = new Date(clientTime.getTime() + Math.random() * 1000);

        mockData.push({
          id: i,
          clientIp: ips[Math.floor(Math.random() * ips.length)],
          logContent: `${logTypes[Math.floor(Math.random() * logTypes.length)]} - 详细信息${i}`,
          clientTime: clientTime.toISOString(),
          serverTime: serverTime.toISOString(),
          userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          details: `详细的日志信息内容，包含操作的具体参数和结果。记录ID: ${i}`
        });
      }

      return mockData;
    },

    // 改变页面大小
    changePageSize(value) {
      this.filter.pageSize = value;
      this.filter.pageIndex = 1;
      this.queryDataList();
    },

    // 改变页码
    changePage(value) {
      this.filter.pageIndex = value;
      this.queryDataList();
    },

    // 处理排序变化
    handleSortChange({ prop, order }) {
      if (order) {
        const sortOrder = order === "ascending" ? "asc" : "desc";
        this.filter.order = `${prop} ${sortOrder}`;
      } else {
        this.filter.order = "serverTime desc"; // 默认排序
      }
      this.queryDataList();
    },

    // 序号计算方法
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1;
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return "";
      return moment(dateTime).format("YYYY-MM-DD HH:mm:ss");
    },

    // 查看详情
    handleShowDetail(id) {
      if (!id) {
        this.$message.error("日志ID不能为空");
        return;
      }

      // 模拟获取详情数据
      const logItem = this.generateMockData().find(item => item.id === id);
      if (logItem) {
        this.detailInfo = {
          clientIp: logItem.clientIp,
          logContent: logItem.logContent,
          clientTime: this.formatDateTime(logItem.clientTime),
          serverTime: this.formatDateTime(logItem.serverTime),
          userAgent: logItem.userAgent,
          details: logItem.details,
        };
        this.showDetail = true;
      }

      // 真实API调用示例（注释掉的代码）
      /*
      this.$http
        .get("/ClientLog/GetClientLog", { params: { id: id } })
        .then((response) => {
          if (response.data && response.data.success) {
            const logDetail = response.data.data;
            this.detailInfo = {
              clientIp: logDetail.clientIp,
              logContent: logDetail.logContent,
              clientTime: this.formatDateTime(logDetail.clientTime),
              serverTime: this.formatDateTime(logDetail.serverTime),
              userAgent: logDetail.userAgent,
              details: logDetail.details,
            };
            this.showDetail = true;
          } else {
            const message =
              response.data?.Message ||
              response.data?.message ||
              "获取日志详情失败";
            this.$message.error(message);
          }
        })
        .catch((error) => {
          this.$message.error("获取日志详情失败");
        });
      */
    },

    // 关闭详情弹窗
    handleShowDetailClose() {
      this.showDetail = false;
      this.detailInfo = {
        clientIp: "",
        logContent: "",
        clientTime: "",
        serverTime: "",
        userAgent: "",
        details: "",
      };
    },
  }
}
</script>