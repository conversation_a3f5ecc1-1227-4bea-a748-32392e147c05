﻿--公告
CREATE TABLE [dbo].[Announcement] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_AD:\Git\BPM\src\NET\BPM\90.Database\Shinsoft.DDI.Database\dbo\Tables\Announcement.sqlnnouncement_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
	[EnumFlags]			        INT					        NOT NULL,
	[EnumImportant]				INT					        NOT NULL,
	[Subject]	                NVARCHAR (100)		        NOT NULL,
    [IsShow]                    BIT                         NOT NULL,
    [StartTime]                 DATETIME                    NULL,           --开始时间，控制显示用，由管理员输入
    [EndTime]                   DATETIME                    NULL,           --结束时间，控制显示用，由管理员输入
    [PublishTime]               DATETIME                    NULL,           --发布时间，纯显示用，由管理员输入
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_Announcement] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Announcement_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'标签',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = N'COLUMN',
    @level2name = N'EnumFlags'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'标题',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = N'COLUMN',
    @level2name = N'Subject'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'重要性',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = N'COLUMN',
    @level2name = N'EnumImportant'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否显示',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = N'COLUMN',
    @level2name = N'IsShow'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'开始时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = N'COLUMN',
    @level2name = N'StartTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'结束时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = N'COLUMN',
    @level2name = N'EndTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'发布时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = N'COLUMN',
    @level2name = N'PublishTime'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'公告',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Announcement',
    @level2type = NULL,
    @level2name = NULL
