<template>
  <!-- 经销商详细配置弹窗 -->
    <el-dialog
      :model-value="visible"
      :title:model-value="dialogTitle"
      width="1200px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-tabs v-model="activeTab" type="card">
        <!-- 基本配置 -->
        <el-tab-pane label="基本配置" name="basic">
          <el-form :model="detailForm" label-width="140px" class="detail-form">
            <!-- 第一行：经销商编号、经销商名称 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="经销商编号">
                  <el-input v-model="detailForm.code" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经销商名称">
                  <el-input v-model="detailForm.name" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第二行：经销商全称 -->
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="经销商全称">
                  <el-input v-model="detailForm.fullName" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第三行：经销商地址 -->
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="经销商地址">
                  <el-input v-model="detailForm.address" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第四行：客户经销商编号、客户经销商编号2 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户经销商编号">
                  <el-input v-model="detailForm.customerCode1" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户经销商编号2">
                  <el-input v-model="detailForm.customerCode2" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第五行：客户经销商编号3、经销商等级 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户经销商编号3">
                  <el-input v-model="detailForm.customerCode3" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经销商等级">
                  <el-input v-model="detailForm.level" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第六行：经销商批次、区域 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="经销商批次">
                  <el-input v-model="detailForm.batch" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="区域">
                  <el-input v-model="detailForm.region" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第七行：状态、文件格式 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="状态">
                  <el-select v-model="detailForm.status" placeholder="请选择">
                    <el-option label="正常" value="正常" />
                    <el-option label="锁定" value="锁定" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文件格式">
                  <el-select v-model="detailForm.fileFormat" placeholder="请选择">
                    <el-option label="CSV" value="CSV" />
                    <el-option label="Excel" value="Excel" />
                    <el-option label="TXT" value="TXT" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第八行：采集方式、文件存放目录名 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="采集方式">
                  <el-select v-model="detailForm.collectMethod" placeholder="请选择">
                    <el-option label="客户端" value="客户端" />
                    <el-option label="FTP" value="FTP" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文件存放目录名">
                  <el-input v-model="detailForm.fileDirectory" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第九行：授权码 -->
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="授权码">
                  <el-input v-model="detailForm.authCode" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第十行：经销商联系信息 -->
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="经销商联系信息">
                  <el-input v-model="detailForm.distributorContact" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第十一行：科园联系信息 -->
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="科园联系信息">
                  <el-input v-model="detailForm.keyuanContact" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <!-- 客户端配置 -->
        <el-tab-pane label="客户端配置" name="customerCode">
          <el-form :model="detailForm" label-width="200px" class="detail-form">
            <!-- 基本运行配置 -->
            <div class="config-section">
              <h4 class="section-title">基本运行配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="运行模式">
                    <el-select v-model="detailForm.runMode" placeholder="请选择">
                      <el-option label="自动模式" value="auto" />
                      <el-option label="手动模式" value="manual" />
                      <el-option label="定时模式" value="scheduled" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="服务端检测频率(分)">
                    <el-input-number v-model="detailForm.serverCheckFrequency" :min="1" :max="1440" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="客户端当前版本">
                    <el-input v-model="detailForm.currentVersion" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="程序是否自动升级">
                    <el-select v-model="detailForm.autoUpgrade" placeholder="请选择">
                      <el-option label="是" value="true" />
                      <el-option label="否" value="false" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="需升级到的版本号">
                    <el-input v-model="detailForm.targetVersion" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="升级程序的下载地址">
                    <el-input v-model="detailForm.upgradeDownloadUrl" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 运行时间配置 -->
            <div class="config-section">
              <h4 class="section-title">运行时间配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="重复方式">
                    <el-select v-model="detailForm.repeatMode" placeholder="请选择">
                      <el-option label="每日" value="daily" />
                      <el-option label="每周" value="weekly" />
                      <el-option label="每月" value="monthly" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="运行的时间点">
                    <el-time-picker v-model="detailForm.runTime" format="HH:mm" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="程序重启时间">
                    <el-time-picker v-model="detailForm.restartTime" format="HH:mm" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 数据源配置 -->
            <div class="config-section">
              <h4 class="section-title">数据源配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="数据源获取方式">
                    <el-select v-model="detailForm.dataSourceType" placeholder="请选择">
                      <el-option label="数据库" value="database" />
                      <el-option label="文件" value="file" />
                      <el-option label="API接口" value="api" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="DB数据源的连接方式">
                    <el-select v-model="detailForm.dbConnectionType" placeholder="请选择">
                      <el-option label="SQL Server" value="sqlserver" />
                      <el-option label="MySQL" value="mysql" />
                      <el-option label="Oracle" value="oracle" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="DB数据源的数据库连接">
                    <el-input v-model="detailForm.dbConnectionString" type="textarea" :rows="2" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="DB数据源买进的SQL">
                    <el-input v-model="detailForm.dbBuyInSql" type="textarea" :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="DB数据源卖出的SQL">
                    <el-input v-model="detailForm.dbSellOutSql" type="textarea" :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="DB数据源库存的SQL">
                    <el-input v-model="detailForm.dbInventorySql" type="textarea" :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 目标上传配置 -->
            <div class="config-section">
              <h4 class="section-title">目标上传配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="目标上传方式">
                    <el-select v-model="detailForm.uploadMethod" placeholder="请选择">
                      <el-option label="HTTP" value="http" />
                      <el-option label="FTP" value="ftp" />
                      <el-option label="SFTP" value="sftp" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="HTTP上传地址">
                    <el-input v-model="detailForm.httpUploadUrl" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="HTTP上传用户名">
                    <el-input v-model="detailForm.httpUploadUsername" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="HTTP上传密码">
                    <el-input v-model="detailForm.httpUploadPassword" type="password" show-password />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 日志记录配置 -->
            <div class="config-section">
              <h4 class="section-title">日志记录配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="日志记录方式">
                    <el-select v-model="detailForm.logRecordMethod" placeholder="请选择">
                      <el-option label="本地文件" value="local" />
                      <el-option label="WebService" value="webservice" />
                      <el-option label="数据库" value="database" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="记录日志的WebService地址">
                    <el-input v-model="detailForm.logWebServiceUrl" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="记录日志的WebService用户名">
                    <el-input v-model="detailForm.logWebServiceUsername" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="记录日志的WebService密码">
                    <el-input v-model="detailForm.logWebServicePassword" type="password" show-password />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 其它参数配置 -->
            <div class="config-section">
              <h4 class="section-title">其它参数配置</h4>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="其它参数">
                    <el-input v-model="detailForm.otherParameters" type="textarea" :rows="4" placeholder="请输入其它参数配置，每行一个参数" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-tab-pane>

        <!-- 规则配置 -->
        <el-tab-pane label="规则配置" name="rules">
          <div class="rules-config-container">
            <!-- 业务类型选项卡 -->
            <el-tabs v-model="activeRuleTab" type="card" class="business-tabs">
              <!-- 销售规则 -->
              <el-tab-pane label="销售" name="sales">
                <div class="rule-section">
                  <div class="rule-group">
                    <h4 class="rule-title">销售规则</h4>
                    <div class="rule-options">
                      <el-button
                        :type="detailForm.salesPreRuleCheck ? 'primary' : 'default'"
                        @click="detailForm.salesPreRuleCheck = !detailForm.salesPreRuleCheck"
                        size="small"
                        class="rule-button"
                      >
                        入库前规则校验
                      </el-button>
                    </div>

                    <div class="validation-options">
                      <el-checkbox v-model="detailForm.salesFileValidation" class="validation-checkbox">
                        文件校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.salesFileValidationExecution" class="validation-checkbox">
                        文件校验执行
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.salesFileValidationSplicing" class="validation-checkbox">
                        文件校验拼接
                      </el-checkbox>
                    </div>

                    <div class="rule-options">
                      <el-button
                        :type="detailForm.salesCleanRuleCheck ? 'primary' : 'default'"
                        @click="detailForm.salesCleanRuleCheck = !detailForm.salesCleanRuleCheck"
                        size="small"
                        class="rule-button"
                      >
                        清洗中规则校验
                      </el-button>
                    </div>

                    <div class="validation-options">
                      <el-checkbox v-model="detailForm.salesStoreNameValidation" class="validation-checkbox">
                        门店名称校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.salesProductValidation" class="validation-checkbox">
                        产品校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.salesQuantityValidation" class="validation-checkbox">
                        数量校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.salesTerminalNameValidation" class="validation-checkbox">
                        终端名称校验
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 库存规则 -->
              <el-tab-pane label="库存" name="inventory">
                <div class="rule-section">
                  <div class="rule-group">
                    <h4 class="rule-title">库存规则</h4>
                    <div class="rule-options">
                      <el-button
                        :type="detailForm.inventoryPreRuleCheck ? 'primary' : 'default'"
                        @click="detailForm.inventoryPreRuleCheck = !detailForm.inventoryPreRuleCheck"
                        size="small"
                        class="rule-button"
                      >
                        入库前规则校验
                      </el-button>
                    </div>

                    <div class="validation-options">
                      <el-checkbox v-model="detailForm.inventoryFileValidation" class="validation-checkbox">
                        文件校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.inventoryFileValidationExecution" class="validation-checkbox">
                        文件校验执行
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.inventoryFileValidationSplicing" class="validation-checkbox">
                        文件校验拼接
                      </el-checkbox>
                    </div>

                    <div class="rule-options">
                      <el-button
                        :type="detailForm.inventoryCleanRuleCheck ? 'primary' : 'default'"
                        @click="detailForm.inventoryCleanRuleCheck = !detailForm.inventoryCleanRuleCheck"
                        size="small"
                        class="rule-button"
                      >
                        清洗中规则校验
                      </el-button>
                    </div>

                    <div class="validation-options">
                      <el-checkbox v-model="detailForm.inventoryStoreNameValidation" class="validation-checkbox">
                        门店名称校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.inventoryProductValidation" class="validation-checkbox">
                        产品校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.inventoryQuantityValidation" class="validation-checkbox">
                        数量校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.inventoryTerminalNameValidation" class="validation-checkbox">
                        终端名称校验
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 购进规则 -->
              <el-tab-pane label="购进" name="purchase">
                <div class="rule-section">
                  <div class="rule-group">
                    <h4 class="rule-title">购进规则</h4>
                    <div class="rule-options">
                      <el-button
                        :type="detailForm.purchasePreRuleCheck ? 'primary' : 'default'"
                        @click="detailForm.purchasePreRuleCheck = !detailForm.purchasePreRuleCheck"
                        size="small"
                        class="rule-button"
                      >
                        入库前规则校验
                      </el-button>
                    </div>

                    <div class="validation-options">
                      <el-checkbox v-model="detailForm.purchaseFileValidation" class="validation-checkbox">
                        文件校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.purchaseFileValidationExecution" class="validation-checkbox">
                        文件校验执行
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.purchaseFileValidationSplicing" class="validation-checkbox">
                        文件校验拼接
                      </el-checkbox>
                    </div>

                    <div class="rule-options">
                      <el-button
                        :type="detailForm.purchaseCleanRuleCheck ? 'primary' : 'default'"
                        @click="detailForm.purchaseCleanRuleCheck = !detailForm.purchaseCleanRuleCheck"
                        size="small"
                        class="rule-button"
                      >
                        清洗中规则校验
                      </el-button>
                    </div>

                    <div class="validation-options">
                      <el-checkbox v-model="detailForm.purchaseStoreNameValidation" class="validation-checkbox">
                        门店名称校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.purchaseProductValidation" class="validation-checkbox">
                        产品校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.purchaseQuantityValidation" class="validation-checkbox">
                        数量校验
                      </el-checkbox>
                      <el-checkbox v-model="detailForm.purchaseTerminalNameValidation" class="validation-checkbox">
                        终端名称校验
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-tab-pane>

        <!-- 列映射配置 -->
        <el-tab-pane label="列映射配置" name="columnMapping">
          <div class="column-mapping-container">
            <!-- 业务类型选项卡 -->
            <el-tabs v-model="activeColumnMappingTab" type="card" class="mapping-tabs">
              <!-- 销售列映射 -->
              <el-tab-pane label="销售" name="sales">
                <div class="mapping-section">
                  <div class="mapping-toolbar">
                    <div class="toolbar-buttons">
                      <el-button type="primary" size="small" icon="Download">
                        导出至当前
                      </el-button>
                      <el-button type="info" size="small" icon="Edit">
                        修改
                      </el-button>
                      <el-button type="success" size="small" icon="Check">
                        保存
                      </el-button>
                      <el-button type="warning" size="small" icon="Refresh">
                        返回
                      </el-button>
                      <el-button type="primary" size="small" icon="Plus">
                        新增
                      </el-button>
                    </div>
                  </div>

                  <div class="mapping-table">
                    <el-table :data="salesColumnMapping" border size="small" class="column-table">
                      <el-table-column type="index" label="序号" width="60" align="center" />
                      <el-table-column prop="fieldName" label="数据库表列名称" width="180" />
                      <el-table-column prop="excelColumn" label="EXCEL列名" width="120" />
                      <el-table-column prop="format" label="格式" width="120" />
                      <el-table-column prop="required" label="必填" width="80" align="center">
                        <template #default="{ row }">
                          <el-switch v-model="row.required" size="small" />
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="120" align="center">
                        <template #default="{ row, $index }">
                          <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" />
                          <el-button icon="Delete" circle size="small" type="danger" @click="deleteMapping($index)" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 库存列映射 -->
              <el-tab-pane label="库存" name="inventory">
                <div class="mapping-section">
                  <div class="mapping-toolbar">
                    <div class="toolbar-buttons">
                      <el-button type="primary" size="small" icon="Download">
                        导出至当前
                      </el-button>
                      <el-button type="info" size="small" icon="Edit">
                        修改
                      </el-button>
                      <el-button type="success" size="small" icon="Check">
                        保存
                      </el-button>
                      <el-button type="warning" size="small" icon="Refresh">
                        返回
                      </el-button>
                      <el-button type="primary" size="small" icon="Plus">
                        新增
                      </el-button>
                    </div>
                  </div>

                  <div class="mapping-table">
                    <el-table :data="inventoryColumnMapping" border size="small" class="column-table">
                      <el-table-column type="index" label="序号" width="60" align="center" />
                      <el-table-column prop="fieldName" label="数据库表列名称" width="180" />
                      <el-table-column prop="excelColumn" label="EXCEL列名" width="120" />
                      <el-table-column prop="format" label="格式" width="120" />
                      <el-table-column prop="required" label="必填" width="80" align="center">
                        <template #default="{ row }">
                          <el-switch v-model="row.required" size="small" />
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="120" align="center">
                        <template #default="{ row, $index }">
                          <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" />
                          <el-button icon="Delete" circle size="small" type="danger" @click="deleteMapping($index)" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 购进列映射 -->
              <el-tab-pane label="购进" name="purchase">
                <div class="mapping-section">
                  <div class="mapping-toolbar">
                    <div class="toolbar-buttons">
                      <el-button type="primary" size="small" icon="Download">
                        导出至当前
                      </el-button>
                      <el-button type="info" size="small" icon="Edit">
                        修改
                      </el-button>
                      <el-button type="success" size="small" icon="Check">
                        保存
                      </el-button>
                      <el-button type="warning" size="small" icon="Refresh">
                        返回
                      </el-button>
                      <el-button type="primary" size="small" icon="Plus">
                        新增
                      </el-button>
                    </div>
                  </div>

                  <div class="mapping-table">
                    <el-table :data="purchaseColumnMapping" border size="small" class="column-table">
                      <el-table-column type="index" label="序号" width="60" align="center" />
                      <el-table-column prop="fieldName" label="数据库表列名称" width="180" />
                      <el-table-column prop="excelColumn" label="EXCEL列名" width="120" />
                      <el-table-column prop="format" label="格式" width="120" />
                      <el-table-column prop="required" label="必填" width="80" align="center">
                        <template #default="{ row }">
                          <el-switch v-model="row.required" size="small" />
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="120" align="center">
                        <template #default="{ row, $index }">
                          <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" />
                          <el-button icon="Delete" circle size="small" type="danger" @click="deleteMapping($index)" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-tab-pane>

        <!-- 采集记录 -->
        <el-tab-pane label="采集记录" name="systemRecord">
          <div class="collection-record-container">
            <!-- 业务类型选项卡 -->
            <el-tabs v-model="activeCollectionTab" type="card" class="collection-tabs">
              <!-- 销售采集记录 -->
              <el-tab-pane label="销售" name="sales">
                <div class="record-section">
                  <div class="record-table">
                    <el-table :data="salesCollectionRecords" border size="small" class="collection-table" v-loading="collectionLoading">
                      <el-table-column prop="collectDate" label="采集日期" width="180" />
                      <el-table-column prop="fileName" label="文件名称" min-width="300">
                        <template #default="{ row }">
                          <el-link type="primary" @click="downloadFile(row.fileName)">{{ row.fileName }}</el-link>
                        </template>
                      </el-table-column>
                      <el-table-column prop="totalRecords" label="记录条数" width="120" align="center" />
                      <el-table-column prop="validRecords" label="正确条数" width="120" align="center" />
                      <el-table-column prop="errorRecords" label="错误条数" width="120" align="center">
                        <template #default="{ row }">
                          <span :class="{ 'error-count': row.errorRecords > 0 }">{{ row.errorRecords }}</span>
                        </template>
                      </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="record-pagination">
                      <el-pagination
                        v-model:current-page="salesPagination.page"
                        v-model:page-size="salesPagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="salesPagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSalesPageSizeChange"
                        @current-change="handleSalesPageChange"
                        small
                      />
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 库存采集记录 -->
              <el-tab-pane label="库存" name="inventory">
                <div class="record-section">
                  <div class="record-table">
                    <el-table :data="inventoryCollectionRecords" border size="small" class="collection-table" v-loading="collectionLoading">
                      <el-table-column prop="collectDate" label="采集日期" width="180" />
                      <el-table-column prop="fileName" label="文件名称" min-width="300">
                        <template #default="{ row }">
                          <el-link type="primary" @click="downloadFile(row.fileName)">{{ row.fileName }}</el-link>
                        </template>
                      </el-table-column>
                      <el-table-column prop="totalRecords" label="记录条数" width="120" align="center" />
                      <el-table-column prop="validRecords" label="正确条数" width="120" align="center" />
                      <el-table-column prop="errorRecords" label="错误条数" width="120" align="center">
                        <template #default="{ row }">
                          <span :class="{ 'error-count': row.errorRecords > 0 }">{{ row.errorRecords }}</span>
                        </template>
                      </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="record-pagination">
                      <el-pagination
                        v-model:current-page="inventoryPagination.page"
                        v-model:page-size="inventoryPagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="inventoryPagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleInventoryPageSizeChange"
                        @current-change="handleInventoryPageChange"
                        small
                      />
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 购进采集记录 -->
              <el-tab-pane label="购进" name="purchase">
                <div class="record-section">
                  <div class="record-table">
                    <el-table :data="purchaseCollectionRecords" border size="small" class="collection-table" v-loading="collectionLoading">
                      <el-table-column prop="collectDate" label="采集日期" width="180" />
                      <el-table-column prop="fileName" label="文件名称" min-width="300">
                        <template #default="{ row }">
                          <el-link type="primary" @click="downloadFile(row.fileName)">{{ row.fileName }}</el-link>
                        </template>
                      </el-table-column>
                      <el-table-column prop="totalRecords" label="记录条数" width="120" align="center" />
                      <el-table-column prop="validRecords" label="正确条数" width="120" align="center" />
                      <el-table-column prop="errorRecords" label="错误条数" width="120" align="center">
                        <template #default="{ row }">
                          <span :class="{ 'error-count': row.errorRecords > 0 }">{{ row.errorRecords }}</span>
                        </template>
                      </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="record-pagination">
                      <el-pagination
                        v-model:current-page="purchasePagination.page"
                        v-model:page-size="purchasePagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="purchasePagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handlePurchasePageSizeChange"
                        @current-change="handlePurchasePageChange"
                        small
                      />
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-tab-pane>

        <!-- 数据记录 -->
        <el-tab-pane label="数据记录" name="dataRecord">
          <div class="data-record-container">
            <!-- 业务类型选项卡 -->
            <el-tabs v-model="activeDataRecordTab" type="card" class="data-record-tabs">
              <!-- 销售数据记录 -->
              <el-tab-pane label="销售" name="sales">
                <div class="data-record-section">
                  <!-- 查询条件 -->
                  <div class="search-form">
                    <el-form :model="salesSearchForm" inline size="small">
                      <el-form-item label="客户">
                        <el-input v-model="salesSearchForm.customer" placeholder="请输入客户" clearable style="width: 150px" />
                      </el-form-item>
                      <el-form-item label="经销商名称">
                        <el-input v-model="salesSearchForm.distributorName" placeholder="请输入经销商名称" clearable style="width: 150px" />
                      </el-form-item>
                      <el-form-item label="日期">
                        <el-date-picker
                          v-model="salesSearchForm.dateRange"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                          style="width: 240px"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" @click="searchSalesData" icon="Search">查询</el-button>
                        <el-button type="success" @click="exportSalesData" icon="Download">导出</el-button>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 数据表格 -->
                  <div class="data-table">
                    <el-table :data="salesDataRecords" border size="small" class="record-table" v-loading="dataRecordLoading">
                      <el-table-column prop="terminalCode" label="终端网点号" width="100" />
                      <el-table-column prop="terminalName" label="终端网点名称" width="120" />
                      <el-table-column prop="terminalAddress" label="终端网点地址" width="100" />
                      <el-table-column prop="customer" label="客户" width="80" />
                      <el-table-column prop="customerCode" label="客户编码" width="100" />
                      <el-table-column prop="batchNumber" label="批次号" width="80" />
                      <el-table-column prop="productName" label="产品名称" width="120" />
                      <el-table-column prop="specification" label="规格" width="100" />
                      <el-table-column prop="unit" label="单位" width="60" />
                      <el-table-column prop="quantity" label="数量" width="80" />
                      <el-table-column prop="unitPrice" label="单价" width="80" />
                      <el-table-column prop="validity" label="效期" width="100" />
                      <el-table-column prop="productionDate" label="生产日期" width="100" />
                      <el-table-column prop="salesDate" label="销售日期" width="100" />
                      <el-table-column prop="hospital" label="医院" width="120" />
                      <el-table-column prop="disease" label="疾病" width="100" />
                      <el-table-column prop="manufacturer" label="生产厂家" width="150" />
                    </el-table>

                    <!-- 分页 -->
                    <div class="data-pagination">
                      <el-pagination
                        v-model:current-page="salesDataPagination.page"
                        v-model:page-size="salesDataPagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="salesDataPagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSalesDataPageSizeChange"
                        @current-change="handleSalesDataPageChange"
                        small
                      />
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 库存数据记录 -->
              <el-tab-pane label="库存" name="inventory">
                <div class="data-record-section">
                  <!-- 查询条件 -->
                  <div class="search-form">
                    <el-form :model="inventorySearchForm" inline size="small">
                      <el-form-item label="客户">
                        <el-input v-model="inventorySearchForm.customer" placeholder="请输入客户" clearable style="width: 150px" />
                      </el-form-item>
                      <el-form-item label="经销商名称">
                        <el-input v-model="inventorySearchForm.distributorName" placeholder="请输入经销商名称" clearable style="width: 150px" />
                      </el-form-item>
                      <el-form-item label="日期">
                        <el-date-picker
                          v-model="inventorySearchForm.dateRange"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                          style="width: 240px"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" @click="searchInventoryData" icon="Search">查询</el-button>
                        <el-button type="success" @click="exportInventoryData" icon="Download">导出</el-button>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 数据表格 -->
                  <div class="data-table">
                    <el-table :data="inventoryDataRecords" border size="small" class="record-table" v-loading="dataRecordLoading">
                      <el-table-column prop="terminalCode" label="终端网点号" width="100" />
                      <el-table-column prop="terminalName" label="终端网点名称" width="120" />
                      <el-table-column prop="terminalAddress" label="终端网点地址" width="100" />
                      <el-table-column prop="customer" label="客户" width="80" />
                      <el-table-column prop="customerCode" label="客户编码" width="100" />
                      <el-table-column prop="batchNumber" label="批次号" width="80" />
                      <el-table-column prop="productName" label="产品名称" width="120" />
                      <el-table-column prop="specification" label="规格" width="100" />
                      <el-table-column prop="unit" label="单位" width="60" />
                      <el-table-column prop="quantity" label="数量" width="80" />
                      <el-table-column prop="unitPrice" label="单价" width="80" />
                      <el-table-column prop="validity" label="效期" width="100" />
                      <el-table-column prop="productionDate" label="生产日期" width="100" />
                      <el-table-column prop="inventoryDate" label="库存日期" width="100" />
                      <el-table-column prop="hospital" label="医院" width="120" />
                      <el-table-column prop="disease" label="疾病" width="100" />
                      <el-table-column prop="manufacturer" label="生产厂家" width="150" />
                    </el-table>

                    <!-- 分页 -->
                    <div class="data-pagination">
                      <el-pagination
                        v-model:current-page="inventoryDataPagination.page"
                        v-model:page-size="inventoryDataPagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="inventoryDataPagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleInventoryDataPageSizeChange"
                        @current-change="handleInventoryDataPageChange"
                        small
                      />
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 购进数据记录 -->
              <el-tab-pane label="购进" name="purchase">
                <div class="data-record-section">
                  <!-- 查询条件 -->
                  <div class="search-form">
                    <el-form :model="purchaseSearchForm" inline size="small">
                      <el-form-item label="客户">
                        <el-input v-model="purchaseSearchForm.customer" placeholder="请输入客户" clearable style="width: 150px" />
                      </el-form-item>
                      <el-form-item label="经销商名称">
                        <el-input v-model="purchaseSearchForm.distributorName" placeholder="请输入经销商名称" clearable style="width: 150px" />
                      </el-form-item>
                      <el-form-item label="日期">
                        <el-date-picker
                          v-model="purchaseSearchForm.dateRange"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                          style="width: 240px"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" @click="searchPurchaseData" icon="Search">查询</el-button>
                        <el-button type="success" @click="exportPurchaseData" icon="Download">导出</el-button>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 数据表格 -->
                  <div class="data-table">
                    <el-table :data="purchaseDataRecords" border size="small" class="record-table" v-loading="dataRecordLoading">
                      <el-table-column prop="terminalCode" label="终端网点号" width="100" />
                      <el-table-column prop="terminalName" label="终端网点名称" width="120" />
                      <el-table-column prop="terminalAddress" label="终端网点地址" width="100" />
                      <el-table-column prop="customer" label="客户" width="80" />
                      <el-table-column prop="customerCode" label="客户编码" width="100" />
                      <el-table-column prop="batchNumber" label="批次号" width="80" />
                      <el-table-column prop="productName" label="产品名称" width="120" />
                      <el-table-column prop="specification" label="规格" width="100" />
                      <el-table-column prop="unit" label="单位" width="60" />
                      <el-table-column prop="quantity" label="数量" width="80" />
                      <el-table-column prop="unitPrice" label="单价" width="80" />
                      <el-table-column prop="validity" label="效期" width="100" />
                      <el-table-column prop="productionDate" label="生产日期" width="100" />
                      <el-table-column prop="purchaseDate" label="购进日期" width="100" />
                      <el-table-column prop="hospital" label="医院" width="120" />
                      <el-table-column prop="disease" label="疾病" width="100" />
                      <el-table-column prop="manufacturer" label="生产厂家" width="150" />
                    </el-table>

                    <!-- 分页 -->
                    <div class="data-pagination">
                      <el-pagination
                        v-model:current-page="purchaseDataPagination.page"
                        v-model:page-size="purchaseDataPagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="purchaseDataPagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handlePurchaseDataPageSizeChange"
                        @current-change="handlePurchaseDataPageChange"
                        small
                      />
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-tab-pane>

        <!-- 客户端日志 -->
        <el-tab-pane label="客户端日志" name="clientConfig">
          <div class="client-log-container">
            <!-- 客户端日志表格 -->
            <div class="client-log-section">
              <el-table :data="clientLogRecords" border size="small" class="client-log-table" v-loading="clientLogLoading">
                <el-table-column prop="clientIp" label="客户端IP" width="150" />
                <el-table-column prop="logContent" label="日志" min-width="400" />
                <el-table-column prop="clientTime" label="客户端时间" width="180" />
                <el-table-column prop="serverTime" label="服务器时间" width="180" />
              </el-table>

              <!-- 分页 -->
              <div class="client-log-pagination">
                <el-pagination
                  v-model:current-page="clientLogPagination.page"
                  v-model:page-size="clientLogPagination.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="clientLogPagination.total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleClientLogPageSizeChange"
                  @current-change="handleClientLogPageChange"
                  small
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 沟通日志 -->
        <el-tab-pane label="沟通日志" name="communicationLog">
          <div class="communication-log-container">
            <!-- 添加沟通日志表单 -->
            <div class="communication-log-form">
              <el-form :model="communicationLogForm" label-width="80px" size="small">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="预约时间">
                      <el-date-picker
                        v-model="communicationLogForm.appointmentTime"
                        type="datetime"
                        placeholder="选择预约时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="沟通日期">
                      <el-date-picker
                        v-model="communicationLogForm.communicationDate"
                        type="datetime"
                        placeholder="选择沟通日期"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="内容">
                      <el-input
                        v-model="communicationLogForm.content"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入沟通内容"
                        maxlength="500"
                        show-word-limit
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item>
                      <el-button type="primary" @click="saveCommunicationLog" icon="Check">保存</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

            <!-- 沟通日志列表 -->
            <div class="communication-log-list">
              <el-table :data="communicationLogRecords" border size="small" class="communication-log-table">
                <el-table-column prop="logTime" label="日志时间" width="180" />
                <el-table-column prop="content" label="内容" min-width="400" />
                <el-table-column label="操作" width="120" align="center">
                  <template #default="scope">
                    <el-button type="warning" size="small" @click="topCommunicationLog(scope.row)" icon="Top">置顶</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDetailDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveDetail" :loading="saveDetailLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script>
export default {
  name: 'DistributorConfigDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    distributorData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'save'],
  data() {
    return {
      saveLoading: false,
      activeTab: 'basic',
      activeRuleTab: 'sales',
      detailForm: {
        code: '',
        name: '',
        fullName: '',
        address: '',
        customerCode1: '',
        customerCode2: '',
        contact: '',
        phone: '',
        email: '',
        fax: '',
        status: '正常',
        collectMethod: '客户端',
        authCode: '',
        // 连接配置字段
        databaseType: 'sqlserver',
        serverAddress: '',
        port: '',
        databaseName: '',
        username: '',
        password: '',
        ftpServer: '',
        ftpPort: '21',
        ftpUsername: '',
        ftpPassword: '',
        httpUploadUrl: '',
        httpUploadUsername: '',
        httpUploadPassword: '',
        logRecordMethod: 'local',
        logWebServiceUrl: '',
        logWebServiceUsername: '',
        logWebServicePassword: '',
        otherParameters: ''
      },
      salesRules: {
        dataSourceType: 'database',
        frequency: 'daily',
        dataSourceConfig: ''
      },
      inventoryRules: {
        dataSourceType: 'database',
        frequency: 'daily',
        dataSourceConfig: ''
      },
      purchaseRules: {
        dataSourceType: 'database',
        frequency: 'daily',
        dataSourceConfig: ''
      },
      salesPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      inventoryPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      purchasePagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      salesDataPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      inventoryDataPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      purchaseDataPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      clientLogPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 数据记录搜索表单
      salesSearchForm: {
        customer: '',
        distributorName: '',
        dateRange: null,
        startDate: '',
        selectAll: false,
        currentDate: false
      },
      inventorySearchForm: {
        customer: '',
        distributorName: '',
        dateRange: null,
        startDate: '',
        selectAll: false,
        currentDate: false
      },
      purchaseSearchForm: {
        customer: '',
        distributorName: '',
        dateRange: null,
        startDate: '',
        selectAll: false,
        currentDate: false
      },
      // 数据记录数据
      salesDataRecords: [],
      inventoryDataRecords: [],
      purchaseDataRecords: [],
      // 沟通日志表单数据
      communicationLogForm: {
        appointmentTime: '',
        communicationDate: '',
        content: ''
      },
      // 沟通日志记录数据
      communicationLogRecords: [
        {
          logTime: '2021-02-23 17:55:40',
          content: '已添加中位学校 (luyao)',
          id: 1
        },
        {
          logTime: '2020-06-09 16:16:46',
          content: '已添加安装培训品。 (luyao)',
          id: 2
        }
      ],
      // 详细配置表单数据
      detailForm: {
        // 基本配置字段
        code: '',
        name: '',
        fullName: '',
        address: '',
        customerCode1: '',
        customerCode2: '',
        customerCode3: '',
        level: '',
        batch: '',
        ruleStatus: '',
        region: '',
        status: '',
        collectMethod: '',
        fileFormat: '',
        fileDirectory: '',
        authCode: '',
        distributorContact: '',
        keyuanContact: '',

        // 客户端配置字段
        runMode: '',
        serverCheckFrequency: 60,
        currentVersion: '',
        autoUpgrade: 'false',
        targetVersion: '',
        upgradeDownloadUrl: '',
        repeatMode: '',
        runTime: '',
        restartTime: '',
        dataSourceType: '',
        dbConnectionType: '',
        dbConnectionString: '',
        dbBuyInSql: '',
        dbSellOutSql: '',
        dbInventorySql: '',
        uploadMethod: '',
        httpUploadUrl: '',
        httpUploadUsername: '',
        httpUploadPassword: '',
        logRecordMethod: '',
        logWebServiceUrl: '',
        logWebServiceUsername: '',
        logWebServicePassword: '',
        otherParameters: '',

        // 规则配置字段 - 销售
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 规则配置字段 - 库存
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 规则配置字段 - 购进
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false
      }
    }
  },
  computed: {
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData();
      }
    },
    distributorData: {
      handler() {
        if (this.visible) {
          this.initFormData();
        }
      },
      deep: true
    }
  },
  methods: {
    initFormData() {
      if (this.distributorData && this.distributorData.id) {
        // 编辑模式，填充现有数据
        this.detailForm = {
          code: this.distributorData.code || '',
          name: this.distributorData.name || '',
          fullName: this.distributorData.fullName || '',
          address: this.distributorData.address || '',
          customerCode1: this.distributorData.customerCode1 || '',
          customerCode2: this.distributorData.customerCode2 || '',
          contact: this.distributorData.contact || '',
          phone: this.distributorData.phone || '',
          email: this.distributorData.email || '',
          fax: this.distributorData.fax || '',
          status: this.distributorData.distributorStatus || '正常',
          collectMethod: this.distributorData.collectMethod || '客户端',
          authCode: this.distributorData.authCode || ''
        };
      } else {
        // 新增模式，重置表单
        this.resetForm();
      }
    },
    
    resetForm() {
      this.detailForm = {
        code: '',
        name: '',
        fullName: '',
        address: '',
        customerCode1: '',
        customerCode2: '',
        contact: '',
        phone: '',
        email: '',
        fax: '',
        status: '正常',
        collectMethod: '客户端',
        authCode: '',
        // 连接配置字段
        databaseType: 'sqlserver',
        serverAddress: '',
        port: '',
        databaseName: '',
        username: '',
        password: '',
        ftpServer: '',
        ftpPort: '21',
        ftpUsername: '',
        ftpPassword: '',
        httpUploadUrl: '',
        httpUploadUsername: '',
        httpUploadPassword: '',
        logRecordMethod: 'local',
        logWebServiceUrl: '',
        logWebServiceUsername: '',
        logWebServicePassword: '',
        otherParameters: ''
      };
      
      this.salesRules = {
        dataSourceType: 'database',
        frequency: 'daily',
        dataSourceConfig: ''
      };
      
      this.inventoryRules = {
        dataSourceType: 'database',
        frequency: 'daily',
        dataSourceConfig: ''
      };
      
      this.purchaseRules = {
        dataSourceType: 'database',
        frequency: 'daily',
        dataSourceConfig: ''
      };
    },
    
    handleSave() {
      this.saveLoading = true;
      
      // 模拟保存操作
      setTimeout(() => {
        const saveData = {
          ...this.detailForm,
          salesRules: this.salesRules,
          inventoryRules: this.inventoryRules,
          purchaseRules: this.purchaseRules
        };
        
        this.saveLoading = false;
        this.$message.success('保存成功');
        this.$emit('save', saveData);
        this.handleClose();
      }, 1000);
    },
    
    handleClose() {
      this.$emit('update:visible', false);
      this.activeTab = 'basic';
      this.activeRuleTab = 'sales';
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.detail-form .el-form-item {
  margin-bottom: 18px;
}

.detail-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.rule-tabs {
  margin-top: 10px;
}

.rule-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.rule-form .el-form-item {
  margin-bottom: 18px;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}
</style>
