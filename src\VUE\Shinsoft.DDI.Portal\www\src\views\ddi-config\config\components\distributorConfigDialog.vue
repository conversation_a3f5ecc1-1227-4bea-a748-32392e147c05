<!--经销商详细配置弹窗组件-->
<template>
  <el-dialog
    v-model="showDialog"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="distributor-config-dialog"
    v-loading="detailLoading"
    element-loading-text="加载配置数据中..."
  >
    <el-tabs v-model="activeTab" type="card">
      <!-- 基本配置 -->
      <el-tab-pane label="基本配置" name="basic">
        <el-form :model="detailForm" label-width="140px" class="detail-tab-content" style="margin-top: 20px;margin-right: 20px;">
          <!-- 第一行：经销商编号、经销商名称 -->
          <el-row :gutter="20" >
            <el-col :span="12">
              <el-form-item label="经销商编号">
                <el-input v-model="detailForm.code" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="经销商名称">
                <el-input v-model="detailForm.name" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：经销商曾用名 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="经销商曾用名">
                <el-input v-model="detailForm.fullName" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：经销商地址 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="经销商地址">
                <el-input v-model="detailForm.distributorAdress" type="textarea" :rows="2" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第七行：状态、文件格式 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态">
                <el-select v-model="detailForm.status" placeholder="请选择">
                  <el-option label="正常" value="正常" />
                  <el-option label="锁定" value="锁定" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件格式">
                <el-select v-model="detailForm.fileFormat" placeholder="请选择">
                  <el-option label="CSV" value="CSV" />
                  <el-option label="Excel" value="Excel" />
                  <el-option label="TXT" value="TXT" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第八行：采集方式、文件存放目录名 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="采集方式">
                <el-select v-model="detailForm.collectMethod" placeholder="请选择">
                  <el-option label="客户端" value="客户端" />
                  <el-option label="FTP" value="FTP" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件存放目录名">
                <el-input v-model="detailForm.fileDirectory" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第九行：授权码 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="授权码">
                <el-input v-model="detailForm.authCode" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第十行：经销商联系信息 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="经销商联系信息">
                <el-input v-model="detailForm.distributorContact" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第十一行：科园联系信息 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="科园联系信息">
                <el-input v-model="detailForm.keyuanContact" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 客户端配置 -->
      <el-tab-pane label="客户端配置" name="customerCode">
        <el-form :model="detailForm" label-width="200px" class="detail-tab-content">
          <!-- 基本运行配置 -->
          <div class="config-section">
            <h4 class="section-title">基本运行配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="运行模式">
                  <el-select v-model="detailForm.runMode" placeholder="请选择">
                    <el-option label="自动模式" value="auto" />
                    <el-option label="手动模式" value="manual" />
                    <el-option label="定时模式" value="scheduled" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="服务端检测频率(分)">
                  <el-input-number v-model="detailForm.serverCheckFrequency" :min="1" :max="1440" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户端当前版本">
                  <el-input v-model="detailForm.currentVersion" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="程序是否自动升级">
                  <el-select v-model="detailForm.autoUpgrade" placeholder="请选择">
                    <el-option label="是" value="true" />
                    <el-option label="否" value="false" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="需升级到的版本号">
                  <el-input v-model="detailForm.targetVersion" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="升级程序的下载地址">
                  <el-input v-model="detailForm.upgradeDownloadUrl" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 运行时间配置 -->
          <div class="config-section">
            <h4 class="section-title">运行时间配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="重复方式">
                  <el-select v-model="detailForm.repeatMode" placeholder="请选择">
                    <el-option label="每日" value="daily" />
                    <el-option label="每周" value="weekly" />
                    <el-option label="每月" value="monthly" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="运行的时间点">
                  <el-time-picker v-model="detailForm.runTime" format="HH:mm" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="程序重启时间">
                  <el-time-picker v-model="detailForm.restartTime" format="HH:mm" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 数据源配置 -->
          <div class="config-section">
            <h4 class="section-title">数据源配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据源获取方式">
                  <el-select v-model="detailForm.dataSourceType" placeholder="请选择">
                    <el-option label="数据库" value="database" />
                    <el-option label="文件" value="file" />
                    <el-option label="API接口" value="api" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="DB数据源的连接方式">
                  <el-select v-model="detailForm.dbConnectionType" placeholder="请选择">
                    <el-option label="SQL Server" value="sqlserver" />
                    <el-option label="MySQL" value="mysql" />
                    <el-option label="Oracle" value="oracle" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源的数据库连接">
                  <el-input v-model="detailForm.dbConnectionString" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源买进的SQL">
                  <el-input v-model="detailForm.dbBuyInSql" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源卖出的SQL">
                  <el-input v-model="detailForm.dbSellOutSql" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源库存的SQL">
                  <el-input v-model="detailForm.dbInventorySql" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 目标上传配置 -->
          <div class="config-section">
            <h4 class="section-title">目标上传配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="目标上传方式">
                  <el-select v-model="detailForm.uploadMethod" placeholder="请选择">
                    <el-option label="HTTP" value="http" />
                    <el-option label="FTP" value="ftp" />
                    <el-option label="SFTP" value="sftp" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="HTTP上传地址">
                  <el-input v-model="detailForm.httpUploadUrl" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="HTTP上传用户名">
                  <el-input v-model="detailForm.httpUploadUsername" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="HTTP上传密码">
                  <el-input v-model="detailForm.httpUploadPassword" type="password" show-password />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 日志记录配置 -->
          <div class="config-section">
            <h4 class="section-title">日志记录配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="日志记录方式">
                  <el-select v-model="detailForm.logRecordMethod" placeholder="请选择">
                    <el-option label="本地文件" value="local" />
                    <el-option label="WebService" value="webservice" />
                    <el-option label="数据库" value="database" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="记录日志的WebService地址">
                  <el-input v-model="detailForm.logWebServiceUrl" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="记录日志的WebService用户名">
                  <el-input v-model="detailForm.logWebServiceUsername" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="记录日志的WebService密码">
                  <el-input v-model="detailForm.logWebServicePassword" type="password" show-password />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 其它参数配置 -->
          <div class="config-section">
            <h4 class="section-title">其它参数配置</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="其它参数">
                  <el-input v-model="detailForm.otherParameters" type="textarea" :rows="4" placeholder="请输入其它参数配置，每行一个参数" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 规则配置 -->
      <el-tab-pane label="规则配置" name="rules">
        <div class="detail-tab-content">
          <!-- 业务类型选项卡 -->
          <el-tabs v-model="activeRuleTab" type="card" class="business-tabs">
            <!-- 销售规则 -->
            <el-tab-pane label="销售" name="sales">
              <div class="rule-section">
                <div>
                  <div class="rule-options">
                    <el-button
                      :type="detailForm.salesPreRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.salesPreRuleCheck = !detailForm.salesPreRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      入库前规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.salesFileValidation" class="validation-checkbox">
                      文件校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesFileValidationExecution" class="validation-checkbox">
                      文件校验执行
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesFileValidationSplicing" class="validation-checkbox">
                      文件校验拼接
                    </el-checkbox>
                  </div>

                  <div class="rule-options">
                    <el-button
                      :type="detailForm.salesCleanRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.salesCleanRuleCheck = !detailForm.salesCleanRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      清洗中规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.salesStoreNameValidation" class="validation-checkbox">
                      门店名称校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesProductValidation" class="validation-checkbox">
                      产品校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesQuantityValidation" class="validation-checkbox">
                      数量校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesTerminalNameValidation" class="validation-checkbox">
                      终端名称校验
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 库存规则 -->
            <el-tab-pane label="库存" name="inventory">
              <div class="rule-section">
                <div class="rule-group">
                  <div class="rule-options">
                    <el-button
                      :type="detailForm.inventoryPreRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.inventoryPreRuleCheck = !detailForm.inventoryPreRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      入库前规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.inventoryFileValidation" class="validation-checkbox">
                      文件校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryFileValidationExecution" class="validation-checkbox">
                      文件校验执行
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryFileValidationSplicing" class="validation-checkbox">
                      文件校验拼接
                    </el-checkbox>
                  </div>

                  <div class="rule-options">
                    <el-button
                      :type="detailForm.inventoryCleanRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.inventoryCleanRuleCheck = !detailForm.inventoryCleanRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      清洗中规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.inventoryStoreNameValidation" class="validation-checkbox">
                      门店名称校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryProductValidation" class="validation-checkbox">
                      产品校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryQuantityValidation" class="validation-checkbox">
                      数量校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryTerminalNameValidation" class="validation-checkbox">
                      终端名称校验
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 购进规则 -->
            <el-tab-pane label="购进" name="purchase">
              <div class="rule-section">
                <div class="rule-group">
                  <div class="rule-options">
                    <el-button
                      :type="detailForm.purchasePreRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.purchasePreRuleCheck = !detailForm.purchasePreRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      入库前规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.purchaseFileValidation" class="validation-checkbox">
                      文件校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseFileValidationExecution" class="validation-checkbox">
                      文件校验执行
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseFileValidationSplicing" class="validation-checkbox">
                      文件校验拼接
                    </el-checkbox>
                  </div>

                  <div class="rule-options">
                    <el-button
                      :type="detailForm.purchaseCleanRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.purchaseCleanRuleCheck = !detailForm.purchaseCleanRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      清洗中规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.purchaseStoreNameValidation" class="validation-checkbox">
                      门店名称校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseProductValidation" class="validation-checkbox">
                      产品校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseQuantityValidation" class="validation-checkbox">
                      数量校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseTerminalNameValidation" class="validation-checkbox">
                      终端名称校验
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>

      <!-- 列映射配置 -->
      <el-tab-pane label="列映射配置" name="columnMapping">
        <div class="detail-tab-content">
          <!-- 业务类型选项卡 -->
          <el-tabs v-model="activeColumnMappingTab" type="card" class="business-tabs">
            <!-- 销售列映射 -->
            <el-tab-pane label="销售" name="sales">
              <div>
                <div class="mapping-table">
                  <el-table :data="salesColumnMapping" border size="small" class="column-table">
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="fieldName" label="数据库表列名称" min-width="200" />
                    <el-table-column prop="excelColumn" label="EXCEL列名" min-width="150" />
                    <el-table-column prop="format" label="格式" min-width="120" />
                    <el-table-column prop="required" label="必填" width="80" align="center">
                      <template #default="{ row }">
                        <el-switch v-model="row.required" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="{ row, $index }">
                        <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" class="edit-mapping-btn" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- 库存列映射 -->
            <el-tab-pane label="库存" name="inventory">
              <div>
                <div class="mapping-table">
                  <el-table :data="inventoryColumnMapping" border size="small" class="column-table">
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="fieldName" label="数据库表列名称" min-width="200" />
                    <el-table-column prop="excelColumn" label="EXCEL列名" min-width="150" />
                    <el-table-column prop="format" label="格式" min-width="120" />
                    <el-table-column prop="required" label="必填" width="80" align="center">
                      <template #default="{ row }">
                        <el-switch v-model="row.required" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="{ row, $index }">
                        <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" class="edit-mapping-btn" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- 购进列映射 -->
            <el-tab-pane label="购进" name="purchase">
              <div>
                <div class="mapping-table">
                  <el-table :data="purchaseColumnMapping" border size="small" class="column-table">
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="fieldName" label="数据库表列名称" min-width="200" />
                    <el-table-column prop="excelColumn" label="EXCEL列名" min-width="150" />
                    <el-table-column prop="format" label="格式" min-width="120" />
                    <el-table-column prop="required" label="必填" width="80" align="center">
                      <template #default="{ row }">
                        <el-switch v-model="row.required" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="{ row, $index }">
                        <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" class="edit-mapping-btn" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>


    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { configApi } from '@/api/configApi'

export default {
  name: 'DistributorConfigDialog',
  props: {
    // 控制对话框显示/隐藏
    modelValue: {
      type: Boolean,
      default: false
    },
    // 对话框标题
    dialogTitle: {
      type: String,
      default: '经销商配置'
    },
    // 表单数据
    detailForm: {
      type: Object,
      default: () => ({})
    },
    // 保存加载状态
    saveLoading: {
      type: Boolean,
      default: false
    },
    // 当前经销商ID
    currentDistributorId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:modelValue', 'save', 'close', 'edit-mapping', 'detail-loaded'],
  data() {
    return {
      activeTab: 'basic',
      activeRuleTab: 'sales',
      activeColumnMappingTab: 'sales',
      detailLoading: false, // 详细数据加载状态
      // 列映射数据
      salesColumnMapping: [
        { fieldName: '经销商编号', excelColumn: 'A', format: '文本', required: true },
        { fieldName: '门店编号', excelColumn: 'B', format: '文本', required: true },
        { fieldName: '门店名称', excelColumn: 'C', format: '文本', required: true },
        { fieldName: '产品编号', excelColumn: 'D', format: '文本', required: true },
        { fieldName: '产品名称', excelColumn: 'E', format: '文本', required: true },
        { fieldName: '销售数量', excelColumn: 'F', format: '数字', required: true },
        { fieldName: '销售金额', excelColumn: 'G', format: '货币', required: true },
        { fieldName: '销售日期', excelColumn: 'H', format: '日期', required: true }
      ],
      inventoryColumnMapping: [
        { fieldName: '经销商编号', excelColumn: 'A', format: '文本', required: true },
        { fieldName: '门店编号', excelColumn: 'B', format: '文本', required: true },
        { fieldName: '门店名称', excelColumn: 'C', format: '文本', required: true },
        { fieldName: '产品编号', excelColumn: 'D', format: '文本', required: true },
        { fieldName: '产品名称', excelColumn: 'E', format: '文本', required: true },
        { fieldName: '库存数量', excelColumn: 'F', format: '数字', required: true },
        { fieldName: '库存金额', excelColumn: 'G', format: '货币', required: true },
        { fieldName: '盘点日期', excelColumn: 'H', format: '日期', required: true }
      ],
      purchaseColumnMapping: [
        { fieldName: '经销商编号', excelColumn: 'A', format: '文本', required: true },
        { fieldName: '门店编号', excelColumn: 'B', format: '文本', required: true },
        { fieldName: '门店名称', excelColumn: 'C', format: '文本', required: true },
        { fieldName: '产品编号', excelColumn: 'D', format: '文本', required: true },
        { fieldName: '产品名称', excelColumn: 'E', format: '文本', required: true },
        { fieldName: '购进数量', excelColumn: 'F', format: '数字', required: true },
        { fieldName: '购进金额', excelColumn: 'G', format: '货币', required: true },
        { fieldName: '购进日期', excelColumn: 'H', format: '日期', required: true }
      ]
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  watch: {
    // 监听对话框显示状态
    modelValue(newVal) {
      if (newVal && this.currentDistributorId) {
        // 对话框打开时，加载详细数据
        this.loadDetailData();
      }
    }
  },
  methods: {
    // 加载详细数据
    async loadDetailData() {
      if (!this.currentDistributorId) {
        return;
      }

      this.detailLoading = true;

      try {
        const response = await configApi.getReceiverClient(this.currentDistributorId);

        if (response.data && response.data.success !== false) {
          // 触发父组件更新详细数据
          this.$emit('detail-loaded', response.data.data || response.data);
        } else {
          this.$message.error(response.data.messages?.[0] || '获取详细配置失败');
        }
      } catch (error) {
        console.error('获取经销商详细配置失败:', error);
        this.$message.error('获取详细配置失败，请稍后重试');
      } finally {
        this.detailLoading = false;
      }
    },

    // 处理关闭对话框
    handleClose() {
      this.$emit('close')
    },
    // 处理保存
    handleSave() {
      this.$emit('save')
    },
    // 编辑列映射
    editMapping(row, index) {
      this.$emit('edit-mapping', row, index)
    },

    // 处理编辑列映射
    editMapping(row, index) {
      this.$emit('edit-mapping', row, index)
    }
  }
}
</script>

<style scoped>
/* 配置区块样式 */
.config-section {
  margin-bottom: 10px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

/* 业务选项卡样式 */
.business-tabs {
  margin-top: 15px;
}

.business-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

/* 规则配置样式 */
.rule-section {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.rule-options {
  margin-bottom: 15px;
}

.rule-button {
  margin-right: 10px;
}

.validation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-left: 20px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.validation-checkbox {
  margin-right: 0;
}

/* 表格通用样式 */
.mapping-table,
.communication-log-list {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.column-table,
.communication-log-table {
  width: 100%;
}

.column-table :deep(.el-table__header),
.communication-log-table :deep(.el-table__header) {
  background-color: #f5f7fa;
}

/* 编辑按钮样式 */
.edit-mapping-btn {
  color: #fd9e00;
  border-color: #fd9e00;
}

.edit-mapping-btn:hover {
  background-color: #fd9e00;
  color: #fff;
}



/* 对话框样式 */
.distributor-config-dialog :deep(.el-dialog__header) {
  background-color: #f5f7fa;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.distributor-config-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.distributor-config-dialog :deep(.el-dialog__body) {
  padding: 0;
}

/* 选项卡通用样式 */
:deep(.el-tabs__header) {
  margin-bottom: 0 !important;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border-bottom: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>
