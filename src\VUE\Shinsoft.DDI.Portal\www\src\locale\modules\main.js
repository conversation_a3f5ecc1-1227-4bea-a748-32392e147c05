// 导航-->中英文
export const main = {
    'zhCN': {
        main: {
            mainLanguageSwitching: '语言切换',
            mainExitSystem: '退出系统',
            updatePassword: '修改密码',
            mainMasterDataManagement: '主数据管理',
            mainProductMasterData: '产品主数据',
            mainProduct: '产品基本信息',
            mainProductlevel: '产品线管理',
            mainProductanother: '产品别名',
            mainBatchimport: '批号导入',
            mainBatchlist: '批号管理',
            mainBatchanother: '批号别名',
            mainGeography: '地理信息管理',
            mainGeographycounty: '区县管理',
            mainGeographyCity: '城市管理',
            mainGeographyarea: '大区管理',
            mainPleaseSelect: '请先选择省份/城市/区县',
            mainNextStep: '请确认完整填写全部必填信息后再点击下一步',
            mainMaterialGroup: '分型查询',
            accountingPriceList: '价格管理',
            doctorManagement: '医生&讲者数据',
            doctorList: '医生&讲者查询',
            // 系统管理
            mainSystemManagement: '系统管理',
            mainPersonne: '人员管理',
            mainCycleManagement: '周期管理',
            mainLogManagement: '日志管理',
            mainAnnouncementManagement: '公告管理',
            mainAnnouncementQuery: '公告管理',
            mainExceptionLogManagement: '异常日志管理',
            mainEventLogManagement: '事件日志管理',
            mainLoginLogManagement: '登录日志管理',
            mainAuthorityManagement: '权限设置',
            systemSetting: '系统设置',
            mainProductRightsGroupManagement: '产品权限组管理',
            mainReceiverType: '收货方类型管理',
            mainDictionaryManagement: '字典管理',
            mainInventoryReportSetup: '库存报表设置',
            mainRoleList: '角色管理',
            mainReceiverInStation: '终端挂岗记录',
            mainEmployeeInStation: '员工上岗记录',
            mainSalesFlowComplaintsQuery: '流向申诉审核',
            mainInvoicingSettings: '库存设置',
            inventoryEstimateSetting: '经销商库存预估设置',
            mainOrderSetting: '订单设置',
            mainMaterialOrderSetting: '物料下单设置',
            mainContractSetting: '合同页脚设置',
            mainTenderingSetting: '招标设置',
            mainMilitaryAreaManagement: '军区管理',
            mainBiddingStateManagement: '招标状态管理',
            mainInventoryCheckDateFeedBackBaseline: '日库存差异反馈基线',
            mainDiscrepancyRateOfInventoryDaily: '日库存差异率设置',
            mainBaselineSettingOfTargetHospital: '目标医院销量基线',
            mainHomeSettings: '首页设置',
            mainDDIContributionRateSetting: 'DDI商业TMS占比',
            mainNoticeDefaultNumberSetting: '公告默认条数设置',
            mainOtherShipperSetting: '其他货主设置',
            mainNotificationSetting: '通知忽略设置',
            masterDataSettings: '主数据设置',
            mainTaskStatus: '服务运行状态',
            // 收货方主数据
            mainReceiver: '收货方主数据',
            mainReceiverManagement: '收货方管理',
            mainReceiverAnotherManagement: '收货方别名管理',
            mainReceiverLog: '收货方日志',
            mainQueryVolumeOfTargetHospital: '目标医院潜力查询',
            mainHcoGapReport: '收货方差异报表',
            mainDrugstoreNearHospitalManagement: '医院周边药店',
            receiverAliasChangeRequestList: '别名变更申请查询',
            receiverAliasChangeRequest: '收货方别名申请',
            targetReceiverManagement: '目标客户数据',
            marketData: '市场数据',
            cpaSaleDate: 'CPA数据',
            targetReceiverList: '目标客户管理',
            departmentProject: '部门项目管理',
            receiverLevelList: '客户分级管理',
            crmDataApprovalList: 'CRM数据审核',
            hospitalProcurementList: '医院进药管理',
            drugstoreList: '院外药店管理',
            // 经销商主数据
            mainDistributor: '流向商业主数据',
            mainExpiringQualificationList: '临期资质',
            mainProductChannelList: '渠道管理',
            mainDistriButorList: '流向商业管理',
            applyList: '变更申请',
            mainMyApplyList: '我的审批',
            confirmList: '变更管理',
            contractorModal: '联系人管理',
            historyBillQuery: '历史单据查询',
            distributorProductQuery: '流向商业产品信息',
            distributorProductQueryByBO: '打单商业查询',

            // 流向管理
            mainSalesFlowManagement: '流向管理',
            mainImportData: '数据导入',
            mainSalesFlowConfirm: '流向数据确认',
            mainSalesFlowDistributorConfirm: '流向商业流向确认',
            mainAutoUpload: '自动日志列表',
            mainManualUpload: '上传日志列表',
            mainDeficiencyInspect: '流向差异性核查',
            mainDownload: '标准模板上传',
            mainDDIImportData: 'DDI数据导入',
            mainPurchaseQuery: '数据查询',
            otherQuery: '运行查询',
            abnormalTerminalReport: '异常终端报表',
            mainMonthlyPurchaseList: '月采购数据查询',
            mainDailyPurchaseList: '日采购数据查询',
            mainSalesFlowDailyStateList: '流向商业日提供情况',
            mainDailySalesFlowQuery: '日流向查询',
            mainDailySalesFlowByTerminalQuery: '销售日流向查询',
            mainMonthlySalesFlowByTerminalQuery: '销售月流向查询',
            mainMonthlySalesFlowDistributorQuery: '月流向查询',
            mainDelistingProductSalesFlowQuery: '退市产品流向查询',
            mainMonthlySalesFlowDistributorSimpleQuery: '商务月流向精简查询',
            mainMonthlySalesFlowPreparatoryQuery: '可追溯商业流向查询',
            mainSalesFlowMonthlyStateList: '流向商业月提供情况',
            mainSalesFlowDelData: '数据删除',
            mainSalesFlowDelDataDailyStatus: '删除数据提供情况',
            mainSalesFlowDelDataMonthlyPurchase: '删除月采购数据',
            mainSalesFlowDelDataDailyPurchase: '删除日采购数据',
            mainSalesFlowDelDataDailyStock: '删除日库存',
            mainSalesFlowDelDataMonthlyStock: '删除月库存',
            mainSalesFlowDelDataDailySalesFlow: '删除日流向',
            mainSalesflowInspect: '流向反馈',
            mainMonthlySAPData: 'SAP月数据生成',
            mainChannelAbnormalInspect: '渠道异常反馈',
            mainDataQuery: '数据查询',
            mainAnalepticQuery: '兴奋剂反馈',
            mainDefaultBatchModification: '默认批号修改',
            mainSuspectedTransfer: '疑似调拨反馈',
            mainTargetHospitalSales: '目标医院预警',
            mainDailySalesFlowSummaryQuery: '每日汇总报告',

            // 进销存管理
            mainInventory: '库存管理',
            mainInventoryReport: '库存报表',
            mainInventoryOtherItemReport: '进销存其它项汇总',
            mainDailyInventory: '日库存',
            mainDailyInventoryQuery: '日库存查询',
            mainInventoryInspect: '库存反馈',
            mainDailyInventoryInspect: '日库存差异反馈',
            mainMonthlyInventoryInspect: '月库存差异核查',
            mainDistributorAmountReport: '流向商业库存金额',
            mainBatchMonthlyInventory: '批号月库存查询',
            inventoryUpdateReport: '库存更新报告',
            inventoryByBatchNumberReport: '批号库存报告',

            // 报表管理
            mainReportManagement: '数据报表',
            mainSalesFlowReport: '流向报表',
            mainAnalysisByReceiverQuery: '按收货方汇总',
            mainAnalysisInMarketQuery: '按年度分型汇总',
            mainAnalysisByReceiverProvince: '按收货方省份汇总',
            mainAnalysisInByDistributor: '按发货方汇总',
            analysisByDistributorAndReceiver: '发货方收货方汇总',
            compareByDistributor: '发货方对比报表',
            compareByReceiver: '收货方对比报表',
            splitDetailQuery: '拆分查询报表',
            // 销退管理
            mainSalesReturnManagement: '销退管理',
            mainSalesReturnOwner: '销退主导人',
            mainSalesReturnReason: '销退原因',
            mainSalesReturnReport: '销退报表',
            mainSalesReturnReply: '销退反馈',
            // 流向管理
            mainSalesFlowSetting: '流向设置',
            mainReplyRadixSetting: '销退反馈基数设置',
            salesReturnReplyRateLineSetting: '退货率基线设置',
            gXPBusinessInformationImport: 'GXP商业信息导入',
            gxpInfoTemplateDownload: 'GXP商业信息模板下载',
            productSettingOfSuspect: '疑似调拨品种设置',
            // 招标管理
            bidding: '招标管理',
            biddingProject: '项目管理',
            tenderPriceQuery: '价格查询',
            biddingDetailQuery: '明细查询',
            biddingEdit: '项目维护',
            projectManagement: '项目管理',
            biddingQueryForRepresentative: '外勤项目维护',
            biddingReport: '招标报表',
            biddingPriceConflict: '价格验证报表',
            provinceMinPrice: '全国省级最低中标价',
            biddingOfDelistingProducts: '退市产品招标报表',
            processStepManagement: '流程步骤管理',
            defaultBidder: '默认投标主体管理',
            biddingTypeManagement: '招标类型管理',
            mainDrugStoreCompensationFile: '招标补偿支持件',
            mainCompensationSummaryList: '补偿单价管理',
            mainDrugStoreSetting: '药店补偿名单管理',
            documentManagement: '常用文档设置',
            confirmUnitPriceDetail: '确认补偿单价',
            compensationForBidding: '招标补偿',
            compensationUnitPrice: '补偿单价',
            mainCertificate: '补偿证明',
            mainCertificateManagement: '补偿证明管理',
            mainReturnRequirement: '退货证明需求',
            certificateQuery: '补偿证明查询',
            mainCertificateDetailRepresentative: '付款渠道查询',
            // 云商管理
            weChatSettings: '云商设置',
            weChatNoticeSettings: '云商通知设置',
            customerManagement: '客户管理',
            billNoticeSetting: '账单提醒通知',
            weChatNoticeSettingDays: '账单提前X天通知设置',
            daysRateNotNull: '天数设置不能为空',
            settingDays: '设置天数',
            sendFeedBackNotice: '反馈提醒通知',
            customerNoticeManagement: '公告通知',
            compensationResult: '补偿结果',
            quarterCompensation: '季度补偿计算',
            accruedExpense: '月度预提',
            // 药店拜访
            visitDrugstore: '药店拜访',
            basicDataManagement: '基础数据维护',
            visitPlan: '巡店计划',
            drugstoreManagement: '药店基础数据',
            drugstoreBUManagement: 'BU与药店基础数据',
            visitResult: '巡店结果',
            SFELogmanagement: 'SFE日志管理',
            salesManagement: '销售管理',
            quotaManagement: '潜力管理',
            stageQuotaManagement: '阶段潜力管理',
            receiverQuotaManagement: '终端潜力管理',
            areaQuotaManagement: '区域潜力查询',
            procurement: '医院进药管理'
        }
    },
    'enUS': {
        main: {
            mainLanguageSwitching: 'Language',
            mainExitSystem: 'Log Out',
            updatePassword: 'Update password',
            mainMasterDataManagement: 'Master data',
            mainProductMasterData: 'Product Management',
            mainProduct: 'Product',
            mainProductlevel: 'Product Line',
            mainProductanother: 'Product Alias',
            mainBatchimport: 'Import Batch Number',
            mainBatchlist: 'Batch Number',
            mainBatchanother: 'Batch Number Alias',
            mainGeography: 'Geographic',
            mainGeographycounty: 'County',
            mainGeographyCity: 'City',
            mainGeographyarea: 'Area',
            mainPleaseSelect: 'Please choose provinces and cities first.',
            mainNextStep: 'Please confirm that you have completed the required information and click on the next step',
            mainMaterialGroup: 'Material Group',
            accountingPriceList: '价格管理',
            mainSalesFlowComplaintsQuery: '流向申诉审核',
            // 系统管理
            mainSystemManagement: 'System',
            mainPersonne: 'Member',
            mainCycleManagement: 'Cycle Management',
            mainLogManagement: 'Log Management',
            mainAnnouncementManagement: 'Announcement Management',
            mainAnnouncementQuery: 'AnnouncementManagement',
            mainExceptionLogManagement: 'Exception Log',
            mainEventLogManagement: 'Event Log',
            mainLoginLogManagement: 'Login Log',
            mainAuthorityManagement: 'Authority Setting',
            systemSetting: 'System Setting',
            mainProductRightsGroupManagement: 'Product Group',
            mainReceiverType: 'ReceiverTypeManagement',
            mainDictionaryManagement: 'Dictionary',
            mainInventoryReportSetup: 'Inventory Report Setting',
            mainRoleList: 'Role',
            mainInvoicingSettings: 'Invoicing Settings',
            inventoryEstimateSetting: 'Inventory Estimate Setting',
            mainOrderSetting: 'Order Setting',
            mainMaterialOrderSetting: 'Material Order Setting',
            mainContractSetting: 'Contract Setting',
            mainTenderingSetting: 'Tendering Setting',
            mainMilitaryAreaManagement: 'Military Area Management',
            mainBiddingStateManagement: 'Bidding State Management',
            mainInventoryCheckDateFeedBackBaseline: 'Inventory Checkdate FeedBack Baseline Setting',
            mainDiscrepancyRateOfInventoryDaily: 'Discrepancy Rate Of InventoryDaily',
            mainBaselineSettingOfTargetHospital: 'Baseline Setting Of TargetHospital',
            mainHomeSettings: 'Home Settings',
            mainDDIContributionRateSetting: 'DDI Contribution Rate Setting',
            mainNoticeDefaultNumberSetting: 'Notice Default Number Setting',
            mainOtherShipperSetting: 'Other Shipper Setting',
            mainNotificationSetting: 'Notification Ignorance Settings',
            masterDataSettings: 'MasterData Setting',
            mainTaskStatus: 'Service running status',
            // 收货方主数据
            mainReceiver: 'Receiver Management',
            mainReceiverManagement: 'Receiver',
            mainReceiverAnotherManagement: 'Receiver Alias',
            mainReceiverLog: 'Receiving Log',
            mainQueryVolumeOfTargetHospital: 'Query Volume Of TargetHospital',
            mainHcoGapReport: 'Ship-to party variance report',
            mainDrugstoreNearHospitalManagement: 'Nearby Hospital Drugstores',
            receiverAliasChangeRequestList: 'Receiver Alias Change Request',
            receiverAliasChangeRequest: 'Receiver Alias Change Request',
            targetReceiverManagement: 'Target Receiver Data',
            targetReceiverList: 'Target Receiver Management',
            departmentProject: 'Department Project Management',
            receiverLevelList: 'Receiver Level Management',
            crmDataApprovalList: 'CRM Data Approval',
            hospitalProcurementList: 'Hospital Procurement Management',
            drugstoreList: 'Drug Store Of Hospital',
            // 经销商主数据
            mainDistributor: 'Distributor Management',
            mainExpiringQualificationList: 'Expired Qualification',
            mainProductChannelList: 'Product Channel',
            mainDistriButorList: 'Distributor',
            applyList: 'Approval Change',
            mainMyApplyList: 'My approval',
            confirmList: 'Confirm Change',
            contractorModal: 'Contact',
            historyBillQuery: 'Distributor Log',
            distributorProductQuery: 'Distributor Product',
            distributorProductQueryByBO: 'Distributor Product For BO',
            // 流向管理
            mainSalesFlowManagement: 'Sales Flow',
            mainImportData: 'Import Data',
            mainSalesFlowConfirm: 'Confirm',
            mainSalesFlowDistributorConfirm: 'Distributor Sales Flow',
            mainAutoUpload: 'Auto Upload Log',
            mainManualUpload: 'Upload Log',
            mainDownload: 'Standard Template',
            mainDDIImportData: 'Import DDI Data',
            mainPurchaseQuery: 'Query',
            otherQuery: 'Other Query',
            mainReceiverInStation: '终端挂岗记录',
            mainEmployeeInStation: '员工上岗记录',
            abnormalTerminalReport: 'Abnormal Terminal Report',
            mainMonthlyPurchaseList: 'Monthly Purchase',
            mainDailyPurchaseList: 'Daily Purchase',
            mainSalesFlowDailyStateList: 'Daily Status',
            mainDailySalesFlowQuery: 'Daily Flow',
            mainDailySalesFlowByTerminalQuery: 'DailyFlow By Terminal',
            mainMonthlySalesFlowByTerminalQuery: 'MonthlyFlow By Terminal',
            mainMonthlySalesFlowDistributorQuery: 'Distributor Sales Flow',
            mainMonthlySalesFlowDistributorSimpleQuery: 'Distributor Simple Sales Flow',
            mainMonthlySalesFlowPreparatoryQuery: 'Preparatory Sales Flow',
            mainSalesFlowMonthlyStateList: 'Month Status',
            mainSalesFlowDelData: 'Delete',
            mainSalesFlowDelDataDailyStatus: 'Delete Daily Status',
            mainSalesFlowDelDataMonthlyPurchase: 'Delete Monthly Purchase',
            mainSalesFlowDelDataDailyPurchase: 'Delete Daily Purchase',
            mainSalesFlowDelDataDailyStock: 'Delete Daily Inventory',
            mainSalesFlowDelDataMonthlyStock: 'Delete Monthly Inventory',
            mainSalesFlowDelDataDailySalesFlow: 'Delete Daily Sales Flow',
            mainSalesflowInspect: 'SalesFlow FeedBack',
            mainDeficiencyInspect: 'Sales flow Deficiency',
            mainMonthlySAPData: 'SAP Monthly Data',
            mainChannelAbnormalInspect: 'ChannelAbnormalFeedBack',
            mainDataQuery: 'Query',
            mainAnalepticQuery: 'StimulantsFeedBack',
            mainDefaultBatchModification: 'Default Batch Number',
            mainSuspectedTransfer: 'Suspected allocation feedback',
            mainTargetHospitalSales: 'Target Hospital Sales',
            mainDailySalesFlowSummaryQuery: 'Daily Sales Flow Summary',

            // 进销存管理
            mainInventory: 'Inventory',
            mainInventoryReport: 'Report',
            mainInventoryOtherItemReport: 'OtherItem Report',
            mainDailyInventory: 'Daily Inventory',
            mainDailyInventoryQuery: 'Daily Inventory',
            mainInventoryInspect: 'Inventory FeedBack',
            mainDailyInventoryInspect: 'DailyInventoryFeedBack',
            mainMonthlyInventoryInspect: 'Monthly Sales Flow Inspect',
            mainDistributorAmountReport: 'Distributor Amount Report',
            inventoryUpdateReport: 'Inventory Update Report',
            inventoryByBatchNumberReport: 'ProductBatch Inventory Report',

            // 报表管理
            mainReportManagement: 'data report',
            mainSalesFlowReport: 'Flow report',
            mainAnalysisByReceiverQuery: 'Summary by ship-to party',
            mainAnalysisInMarketQuery: 'Summary by annual classification',
            mainAnalysisByReceiverProvince: 'Summary by the province of the receiving party',
            mainAnalysisInByDistributor: 'Summary by shipper',
            analysisByDistributorAndReceiver: 'Shippers receiver summary',
            compareByDistributor: 'Shipper comparison report',
            compareByReceiver: 'Consignee comparison report',
            splitDetailQuery: 'Split details report',

            // 销退管理
            mainSalesReturnManagement: 'SalesReturnManagement',
            mainSalesReturnOwner: 'SalesReturnOwner',
            mainSalesReturnReason: 'SalesReturnReason',
            mainSalesReturnReport: 'SalesReturnReport',
            mainSalesReturnReply: 'SalesReturnReply',
            // 流向管理
            mainSalesFlowSetting: 'SalesFlow Set Up',
            mainReplyRadixSetting: 'Feedback cardinality setting',
            salesReturnReplyRateLineSetting: 'Sales Return Reply RateLine Setting',
            gXPBusinessInformationImport: 'GXP Business Information Import',
            gxpInfoTemplateDownload: 'GXPInformation TemplateDownload',
            productSettingOfSuspect: 'Product Setting Of Suspect',

            // 招标管理
            bidding: 'Bidding',
            biddingProject: 'Bidding Project',
            tenderPriceQuery: 'Tender Price Query',
            biddingDetailQuery: 'Bidding Detail Query',
            biddingEdit: 'Bidding Edit',
            projectManagement: 'Project management',
            biddingReport: 'Bidding Report',
            biddingQueryForRepresentative: 'Bindding Query For Representative',
            biddingPriceConflict: 'Bidding Price Conflict',
            provinceMinPrice: 'Minimum Winning Bid Price At Provincial Level',
            biddingOfDelistingProducts: 'BiddingOfDelistingProducts',
            documentManagement: 'Document Management',
            confirmUnitPriceDetail: 'Confirm compensation unit price',
            compensationForBidding: 'Compensation for bidding',
            compensationUnitPrice: 'Compensation unit price',
            mainCertificate: 'Proof of compensation',
            mainCertificateManagement: 'Compensation certificate management',
            mainReturnRequirement: 'Return proof requirements',
            processStepManagement: 'Process step management',
            defaultBidder: 'Default bidder management',
            biddingTypeManagement: 'Bidding Type Management',
            mainDrugStoreCompensationFile: 'Pharmacy compensation support',
            mainCompensationSummaryList: 'Compensation summary record query',
            mainDrugStoreSetting: 'Pharmacy compensation list maintenance',
            certificateQuery: 'Certificate Query',
            mainCertificateDetailRepresentative: 'Payment Channel Quer ',
            // 云商管理
            weChatSettings: 'WeChat Settings',
            weChatNoticeSettings: 'WeChat Notice Settings',
            customerManagement: 'Customer Management',
            billNoticeSetting: 'Bill Notice Setting',
            sendFeedBackNotice: 'FeedBack Notice',
            weChatNoticeSettingDays: 'Bills advance X-day notification settings',
            daysRateNotNull: 'Setting days cannot be empty',
            settingDays: 'Setting Days',
            customerNoticeManagement: 'Customer Notice',
            compensationResult: 'Compensation Result',
            quarterCompensation: 'Quarter Compensation Calculation',
            accruedExpense: 'Accrued Expense',
            // 药店拜访
            visitDrugstore: 'Visit Drugstore',
            basicDataManagement: 'Basic Data Management',
            visitPlan: 'Visit Plan',
            drugstoreManagement: 'Drugstore Management',
            drugstoreBUManagement: 'Drugstore and BU Management',
            visitResult: 'Visit Result',
            SFELogmanagement: 'SFE Log Management',
            salesManagement: '销售管理',
            quotaManagement: '潜力管理',
            stageQuotaManagement: '阶段潜力管理',
            receiverQuotaManagement: '终端潜力管理',
            areaQuotaManagement: '区域潜力查询',
            procurement: '医院进药管理'
        }
    }
}
export default main
