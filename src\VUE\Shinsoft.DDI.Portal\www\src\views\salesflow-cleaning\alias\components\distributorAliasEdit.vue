<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑收货方别名' : '新增收货方别名'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      size="default"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="收货方编码:" prop="distributorCode">
            <el-input
              v-model="formData.distributorCode"
              placeholder="请输入收货方编码"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="收货方名称:" prop="distributorName">
            <el-input
              v-model="formData.distributorName"
              placeholder="请输入收货方名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="收货方别名:" prop="distributorAlias">
            <el-input
              v-model="formData.distributorAlias"
              placeholder="请输入收货方别名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="省份:" prop="province">
            <el-input
              v-model="formData.province"
              placeholder="请输入省份"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="城市:" prop="city">
            <el-input
              v-model="formData.city"
              placeholder="请输入城市"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态:" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="启用" value="启用" />
              <el-option label="禁用" value="禁用" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'DistributorAliasEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'success'],
  data() {
    return {
      loading: false,
      formData: {
        id: null,
        distributorCode: '',
        distributorName: '',
        distributorAlias: '',
        province: '',
        city: '',
        status: '启用',
        remark: ''
      },
      rules: {
        distributorCode: [
          { required: true, message: '请输入收货方编码', trigger: 'blur' }
        ],
        distributorName: [
          { required: true, message: '请输入收货方名称', trigger: 'blur' }
        ],
        distributorAlias: [
          { required: true, message: '请输入收货方别名', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    isEdit() {
      return this.editData && this.editData.id
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      } else {
        this.resetForm()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.isEdit) {
        this.formData = { ...this.editData }
      } else {
        this.resetForm()
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        distributorCode: '',
        distributorName: '',
        distributorAlias: '',
        province: '',
        city: '',
        status: '启用',
        remark: ''
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    },

    // 关闭对话框
    handleClose() {
      this.$emit('update:visible', false)
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true
          
          // 模拟API调用
          setTimeout(() => {
            const data = { ...this.formData }
            
            if (!this.isEdit) {
              // 新增时生成ID
              data.id = Date.now()
              data.createTime = new Date().toLocaleString()
              data.updateTime = new Date().toLocaleString()
            } else {
              // 编辑时更新时间
              data.updateTime = new Date().toLocaleString()
            }

            this.$emit('success', data)
            this.$message.success(this.isEdit ? '更新成功' : '保存成功')
            this.loading = false
            this.handleClose()
          }, 500)
        }
      })
    }
  }
}
</script>
