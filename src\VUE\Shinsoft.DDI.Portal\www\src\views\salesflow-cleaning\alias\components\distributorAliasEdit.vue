<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="收货方别名管理"
    width="550px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 注意事项提示 -->
    <div class="notice-alert">
      <el-alert
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <span class="notice-text">
            注意：修改收货方别名后仅会更新流向中的收货方信息，流向的销售类型不会更新，需人工检查相关流向。
          </span>
        </template>
      </el-alert>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      size="default"
      class="alias-form"
    >
      <!-- 发货方名称 -->
      <el-form-item label="发货方名称" prop="senderName" class="required-field">
        <div class="input-with-button">
          <el-input
            v-model="formData.senderName"
            placeholder="发货方名称"
            readonly
          />
          <el-button
            type="primary"
            :icon="Search"
            class="select-button"
            @click="handleSelectSender"
          >
            选择
          </el-button>
        </div>
      </el-form-item>

      <!-- 收货方别名 -->
      <el-form-item label="收货方别名" prop="distributorAlias" class="required-field">
        <el-input
          v-model="formData.distributorAlias"
          placeholder="收货方别名"
        />
      </el-form-item>

      <!-- 收货方名称 -->
      <el-form-item label="收货方名称" prop="distributorName" class="required-field">
        <div class="input-with-button">
          <el-input
            v-model="formData.distributorName"
            placeholder="收货方名称"
            readonly
          />
          <el-button
            type="primary"
            :icon="Search"
            class="select-button"
            @click="handleSelectReceiver"
          >
            选择
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :icon="Close">
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :icon="Check">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { Search, Check, Close } from '@element-plus/icons-vue'

export default {
  name: 'DistributorAliasEdit',
  components: {
    Search,
    Check,
    Close
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'success'],
  data() {
    return {
      loading: false,
      Search,
      Check,
      Close,
      formData: {
        id: null,
        senderName: '',
        senderCode: '',
        distributorName: '',
        distributorCode: '',
        distributorAlias: ''
      },
      rules: {
        senderName: [
          { required: true, message: '请选择发货方名称', trigger: 'blur' }
        ],
        distributorName: [
          { required: true, message: '请选择收货方名称', trigger: 'blur' }
        ],
        distributorAlias: [
          { required: true, message: '请输入收货方别名', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    isEdit() {
      return this.editData && this.editData.id
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      } else {
        this.resetForm()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.isEdit) {
        this.formData = { ...this.editData }
      } else {
        this.resetForm()
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        senderName: '',
        senderCode: '',
        distributorName: '',
        distributorCode: '',
        distributorAlias: ''
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    },

    // 选择发货方
    handleSelectSender() {
      // TODO: 打开发货方选择对话框
      this.$message.info('选择发货方功能待实现')
      console.log('选择发货方')
    },

    // 选择收货方
    handleSelectReceiver() {
      // TODO: 打开收货方选择对话框
      this.$message.info('选择收货方功能待实现')
      console.log('选择收货方')
    },

    // 关闭对话框
    handleClose() {
      this.$emit('update:visible', false)
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true

          // 模拟API调用
          setTimeout(() => {
            const data = { ...this.formData }

            if (!this.isEdit) {
              // 新增时生成ID
              data.id = Date.now()
              data.createTime = new Date().toLocaleString()
              data.updateTime = new Date().toLocaleString()
            } else {
              // 编辑时更新时间
              data.updateTime = new Date().toLocaleString()
            }

            this.$emit('success', data)
            this.$message.success(this.isEdit ? '更新成功' : '保存成功')
            this.loading = false
            this.handleClose()
          }, 500)
        }
      })
    }
  }
}
</script>
