﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.Core.Configuration;

namespace Shinsoft.DDI.Common.Configration
{
    public abstract class Config : BaseConfig
    {
        public static new string GetConnectionString(string key)
        {
            var connStr = BaseConfig.GetConnectionString(key);

            return DecryptConnStr(connStr);
        }

        public static string DecryptConnStr(string connStr)
        {
            if (!connStr.Contains("SERVER", StringComparison.OrdinalIgnoreCase) && !connStr.Contains(' '))
            {
                // 已加密，需解密

                connStr = AesHelper.Decrypt(connStr, Config.Secret.AesKey);
            }

            return connStr;
        }

        public static Guid ShinsoftId => AppSetting.GetMemberValue(new Guid("*************-8888-8888-************"));

        public static Guid DefaultCompanyId => AppSetting.GetMemberValue(new Guid("*************-8888-8888-************"));

        /// <summary>
        /// 标题
        /// </summary>
        public static string Title => Config.Debug ? $"【测试版】{AppSetting.GetMemberValue()}" : AppSetting.GetMemberValue();

        public static readonly List<EmployeeStatus> AllowLoginEmployeeStatus = [EmployeeStatus.InService];

        public static string MobileRegex => AppSetting.GetMemberValue(@"^1[356789]\d{9}$");

        public static string EmailRegex => AppSetting.GetMemberValue(@"^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$");

        public static string SysRedis => AppSetting.GetMemberValue();

        public static string DefaultPwdFormat => AppSetting.GetMemberValue(@"XL@yyyyMM");

        public abstract partial class SDR
        {
            public static bool Debug => AppSetting.GetValue("SDR:Debug", false);

            public static string CodeHeader => AppSetting.GetValue("SDR:CodeHeader", "SdrCode");

            public static string TokenHeader => AppSetting.GetValue("SDR:TokenHeader", "SdrToken");
        }

        public abstract partial class SitePath
        {
            public static string Base => AppSetting.GetValue("SitePath:Base");

            public static string Api => AppSetting.GetValue("SitePath:Api", "api");

            private static string? _ApiUri = null;

            public static string ApiUri
            {
                get
                {
                    if (_ApiUri == null)
                    {
                        var uri = new StringBuilder();

                        var path = Api.Trim().TrimEnd('/');

                        if (
                            path.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase)
                            || path.StartsWith("https://", StringComparison.InvariantCultureIgnoreCase)
                        )
                        {
                            uri.Append(path);
                        }
                        else
                        {
                            var baseUri = Base.Trim().TrimEnd('/');

                            uri.Append(baseUri).Append('/');

                            if (!path.IsEmpty())
                            {
                                uri.Append(path).Append('/');
                            }
                        }

                        _ApiUri = uri.ToString();
                    }

                    return _ApiUri;
                }
            }

            public static string Mobile => AppSetting.GetValue("SitePath:Mobile", "");

            private static string? _MobileUri = null;

            public static string MobileUri
            {
                get
                {
                    if (_MobileUri == null)
                    {
                        var uri = new StringBuilder();

                        var path = Mobile.Trim().TrimEnd('/');

                        if (
                            path.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase)
                            || path.StartsWith("https://", StringComparison.InvariantCultureIgnoreCase)
                        )
                        {
                            uri.Append(path);
                        }
                        else
                        {
                            var baseUri = Base.Trim().TrimEnd('/');

                            uri.Append(baseUri).Append('/');

                            if (!path.IsEmpty())
                            {
                                uri.Append(path).Append('/');
                            }
                        }

                        _MobileUri = uri.ToString();
                    }

                    return _MobileUri;
                }
            }

            public static string PC => AppSetting.GetValue("SitePath:PC", "pc");

            private static string? _PcUri = null;

            public static string PcUri
            {
                get
                {
                    if (_PcUri == null)
                    {
                        var uri = new StringBuilder();

                        var path = PC.Trim().TrimEnd('/');

                        if (
                            path.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase)
                            || path.StartsWith("https://", StringComparison.InvariantCultureIgnoreCase)
                        )
                        {
                            uri.Append(path);
                        }
                        else
                        {
                            var baseUri = Base.Trim().TrimEnd('/');

                            uri.Append(baseUri).Append('/');

                            if (!path.IsEmpty())
                            {
                                uri.Append(path).Append('/');
                            }
                        }

                        _PcUri = uri.ToString();
                    }

                    return _PcUri;
                }
            }
        }

        public abstract partial class File
        {
            public static string ImportFilePath => AppSetting.GetValue("File:ImportFilePath");

            public static string PdfSavePath => AppSetting.GetValue("File:PdfSavePath");
        }

        public new class SsoSite : BaseConfig.SsoSite { }

        public new class Mail : BaseConfig.Mail { }
    }
}