<!--
/**
 * 货主管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑货主' : '新增货主'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="shipperFormRef"
      :model="shipperForm"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <!-- 货主编码 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="货主编码" prop="Code">
            <el-input v-model="shipperForm.Code" placeholder="请输入货主编码" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="isEdit ? 12 : 24">
          <el-form-item label="货主名称" prop="Name">
            <el-input v-model="shipperForm.Name" placeholder="请输入货主名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="简称" prop="ShortName">
            <el-input v-model="shipperForm.ShortName" placeholder="请输入简称" />
          </el-form-item>
        </el-col>
        <!-- 状态 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="状态" prop="EnumStatus">
            <el-select v-model="shipperForm.EnumStatus" placeholder="请选择状态" style="width: 100%" :disabled="true">
              <el-option label="启用" :value="0" />
              <el-option label="停用" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 联系信息 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="地址" prop="Address">
            <el-input v-model="shipperForm.Address" placeholder="请输入地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="联系人" prop="ContactPerson">
            <el-input v-model="shipperForm.ContactPerson" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="Telephone">
            <el-input v-model="shipperForm.Telephone" placeholder="请输入电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="邮箱" prop="EMail">
            <el-input v-model="shipperForm.EMail" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'shipperDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const shipperFormRef = ref(null)
    
    // 保存加载状态
    const saveLoading = ref(false)
    
    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })
    
    // 表单数据
    const shipperForm = reactive({
      Code: '',
      Name: '',
      ShortName: '',
      Address: '',
      Telephone: '',
      EMail: '',
      ContactPerson: '',
      EnumStatus: 0
    })

    // 表单验证规则 - 动态规则，根据编辑模式调整
    const formRules = computed(() => ({
      Code: isEdit.value ? [
        { required: true, message: '请输入货主编码', trigger: 'blur' },
        { max: 50, message: '编码长度不能超过50个字符', trigger: 'blur' }
      ] : [],
      Name: [
        { required: true, message: '请输入货主名称', trigger: 'blur' },
        { max: 200, message: '名称长度不能超过200个字符', trigger: 'blur' }
      ],
      ShortName: [
        { required: true, message: '请输入简称', trigger: 'blur' },
        { max: 50, message: '简称长度不能超过50个字符', trigger: 'blur' }
      ],
      EnumStatus: isEdit.value ? [
        { required: true, message: '请选择状态', trigger: 'change' }
      ] : [],
      Address: [
        { max: 500, message: '地址长度不能超过500个字符', trigger: 'blur' }
      ],
      Telephone: [
        { max: 50, message: '电话长度不能超过50个字符', trigger: 'blur' }
      ],
      EMail: [
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
        { max: 50, message: '邮箱长度不能超过50个字符', trigger: 'blur' }
      ],
      ContactPerson: [
        { max: 50, message: '联系人长度不能超过50个字符', trigger: 'blur' }
      ]
    }))

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(shipperForm, {
        Code: '',
        Name: '',
        ShortName: '',
        Address: '',
        Telephone: '',
        EMail: '',
        ContactPerson: '',
        EnumStatus: isEdit.value ? 0 : null // 新增时不设置默认状态
      })

      if (shipperFormRef.value) {
        shipperFormRef.value.resetFields()
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = (recordId) => {
      // TODO: 实际项目中需要调用API接口获取数据
      // this.$http.get(`/Shipper/GetShipper/${recordId}`).then(response => {
      //   if (response.success) {
      //     Object.assign(shipperForm, response.data);
      //   } else {
      //     ElMessage.error(response.message || '加载数据失败');
      //   }
      // });

      // 模拟数据加载
      console.log('加载货主数据，ID:', recordId)
      setTimeout(() => {
        const mockData = {
          Code: 'SH001',
          Name: '北京医药货主有限公司',
          ShortName: '北京医药',
          Address: '北京市朝阳区建国门外大街1号',
          Telephone: '010-85123456',
          EMail: '<EMAIL>',
          ContactPerson: '张经理',
          EnumStatus: 0
        }
        Object.assign(shipperForm, mockData)
      }, 100)
    }

    /**
     * 保存货主信息
     */
    const handleSave = () => {
      shipperFormRef.value.validate((valid) => {
        if (valid) {
          saveLoading.value = true

          // TODO: 实际项目中需要调用API接口
          // const apiUrl = isEdit.value ? '/Shipper/UpdateShipper' : '/Shipper/CreateShipper';
          // this.$http.post(apiUrl, shipperForm).then(response => {
          //   if (response.success) {
          //     ElMessage.success(isEdit.value ? '货主信息更新成功' : '货主添加成功');
          //     emit('success');
          //     handleClose();
          //   } else {
          //     ElMessage.error(response.message || '操作失败');
          //   }
          //   saveLoading.value = false;
          // }).catch(error => {
          //   ElMessage.error('操作失败：' + error.message);
          //   saveLoading.value = false;
          // });

          // 模拟保存操作
          setTimeout(() => {
            ElMessage.success(isEdit.value ? '货主信息更新成功' : '货主添加成功')
            saveLoading.value = false
            emit('success')
            handleClose()
          }, 1000)
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      }
    })

    return {
      shipperFormRef,
      saveLoading,
      isEdit,
      dialogVisible,
      shipperForm,
      formRules,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>
