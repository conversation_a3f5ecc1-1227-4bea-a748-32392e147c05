# 项目源代码更新规则

## 主分支：master

* 用于常规开发的分支
* 开发时项目成员可直接推送代码至此分支
* 代码必须编译通过方可推送至服务器端
* 生产环境发布后，必须立刻将代码同步合并至生产分支（prd）

## 生产分支：prd

* 用于维护生产环境的分支
* 维护时项目成员可直接推送代码至此分支
* 代码必须编译通过方可推送至服务器端
* 需要修改代码时必须从主分支同步代码至此分支
* 本分支未经过测试不允许同步至主分支

## 其他分支

* 用于特定功能开发的分支（按需创建）
* 开发时项目成员可直接推送代码至此分支
* 代码必须编译通过方可推送至服务器端
* 本分支未经过测试不允许同步至主分支
* 特定功能开发完毕上线后，如无特殊情况则可删除本分支

# 开发规范

编译必须 **无错误**，**无警告** 方可提交代码

## NET项目

## .NET 8

需使用 [Visual Studio 2022](https://visualstudio.microsoft.com/zh-hans/vs/) v17.8 以上版本打开

# VUE项目

## node.js版本

v20.18.0

## pnpm

安装命令：npm install -g pnpm

## node_modules

可以直接通过命令（pnpm install）安装
暂未发现安装不了的问题

## icon 图标

https://icones.js.org/

# 分支代码合并

在TFS网页端使用拉取请求合并分支代码

单击**代码**中的**拉取请求**页，然后单击“新建拉取请求”，以创建从主题分支到主分支的新拉取请求。

添加好详细信息后，单击“创建拉取请求”。 发送拉取请求后，审阅者将能够看到你的更改，并对修改提供建议，甚至推送后续提交。

第一次创建拉取请求?   [了解详细信息] (http://go.microsoft.com/fwlink/?LinkId=533211&clcid=0x804)

# 推荐软件

* [Visual Studio](https://visualstudio.microsoft.com/zh-hans/vs/)：NET开发IDE
* [Visual Studio Code](https://code.visualstudio.com/Download) ：VUE开发IDE
* [Git](https://git-scm.com/downloads) ：Git
* [TortoiseGit](https://tortoisegit.org/download/)：Git管理器(Windows环境)
* [nvm-windows](https://github.com/coreybutler/nvm-windows)：nodejs版本管理 （[国内下载](https://nvm.uihtm.com/download.html)）

然后将此存储库克隆到本地计算机以开始自己的项目。

编码愉快!
