using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using OfficeOpenXml;
using System.Data;
 

namespace Shinsoft.DDI.Common
{
    /// <summary>
    /// Excel 操作帮助类
    /// 提供 Excel 文件的读取和写入功能
    /// </summary>
    public class ExcelHelper
    {
        private static ExcelPackage WriteToExcel(DataTable dt, string sheetName, ExcelPackage? package = null,
    int startRowNum = 1, int startColNum = 1, bool hasTitle = true,
    eWorkSheetHidden hidden = eWorkSheetHidden.Visible)
        {
            if (package == null)
            {
                package = new ExcelPackage();
            }

            var sheet = package.Workbook.Worksheets[sheetName] ?? package.Workbook.Worksheets.Add(sheetName);

            var rowIndex = hasTitle ? startRowNum + 1 : startRowNum;

            foreach (DataColumn column in dt.Columns)
            {
                SetSheetColumnDefaultStyle(sheet, column.Ordinal + startColNum, column.DataType);
            }

            if (hasTitle)
            {
                foreach (DataColumn column in dt.Columns)
                {
                    var range = sheet.Cells[startRowNum, column.Ordinal + startColNum];
                    range.Value = column.ColumnName;
                    range.Style.Numberformat.Format = "@";
                }
            }

            foreach (DataRow row in dt.Rows)
            {
                foreach (DataColumn column in dt.Columns)
                {
                    var range = sheet.Cells[rowIndex, column.Ordinal + startColNum];
                    range.Value = row[column];
                }

                rowIndex++;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();

            sheet.Hidden = hidden;

            return package;
        }

        public static byte[] WriteToExcelBytes(DataTable dt, string sheetName,
            ExcelPackage? package = null, int startRowNum = 1, int startColNum = 1, bool hasTitle = true,
            eWorkSheetHidden hidden = eWorkSheetHidden.Visible)
        {
            try
            {
                package = WriteToExcel(dt, sheetName, package, startRowNum, startColNum, hasTitle, hidden);
                return package.GetAsByteArray();
            }
            finally
            {
                package?.Dispose();
            }

        }

        private static ExcelPackage WriteToExcel<T>(List<T> list, Dictionary<string, string> columns, string sheetName,
           ExcelPackage? package = null, int startRowNum = 1, int startColNum = 1, bool hasTitle = true,
           eWorkSheetHidden hidden = eWorkSheetHidden.Visible,
           Dictionary<string, string>? additionalColumns = null, Func<T, string, string>? additionalValue = null, string[]? convertTimeColumns = null, bool isAutoFormatDecimal = false)
           where T : class
        {
            if (package == null)
            {
                package = new ExcelPackage();
            }

            var sheet = package.Workbook.Worksheets[sheetName] ?? package.Workbook.Worksheets.Add(sheetName);

            var rowIndex = hasTitle ? startRowNum + 1 : startRowNum;

            var properties = list.GetPropertyInfo(columns);

            int colIndex = startColNum;

            foreach (var p in properties.Select(property => property.Value.Last()))
            {
                SetSheetColumnDefaultStyle(sheet, colIndex, p.PropertyType);
                colIndex++;
            }

            if (hasTitle)
            {
                colIndex = startColNum;
                foreach (var property in properties)
                {
                    var range = sheet.Cells[startRowNum, colIndex];
                    range.Value = property.Key;
                    range.Style.Numberformat.Format = "@";
                    colIndex++;
                }

                #region 额外列

                if (additionalColumns != null && additionalValue != null)
                {
                    foreach (var item in additionalColumns)
                    {
                        var range = sheet.Cells[startRowNum, colIndex];
                        range.Value = item.Key;
                        range.Style.Numberformat.Format = "@";
                        colIndex++;
                    }
                }

                #endregion 额外列
            }

            foreach (var item in list)
            {
                colIndex = startColNum;
                foreach (var property in properties)
                {
                    var addTime = property.Value.Any(p => convertTimeColumns?.Contains(p.Name) == true);

                    var value = item.GetValue(property.Value);
                    var range = sheet.Cells[rowIndex, colIndex];

                    if (value is DateTime || value is DateTime?)
                    {
                        value = addTime ? ((DateTime)value).FormatDateTime() : ((DateTime)value).FormatDate();
                    }
                    else if (value is DateTimeOffset || value is DateTimeOffset?)
                    {
                        value = addTime ? ((DateTimeOffset)value).FormatDateTime() : ((DateTimeOffset)value).FormatDate();
                    }
                    var type = property.Value.Last().PropertyType;
                    if (type == typeof(decimal) || type == typeof(double) || type == typeof(float)
                    || type == typeof(decimal?) || type == typeof(double?) || type == typeof(float?))
                    {
                        if (isAutoFormatDecimal)
                        {
                            if (value != null)
                            {
                                var valueString = value.ToString();
                                if (!string.IsNullOrEmpty(valueString))
                                {
                                    var charIndex = valueString.IndexOf('.');
                                    //获取小数位
                                    var count = valueString.Substring(charIndex + 1).Length;
                                if (charIndex > 0)
                                {
                                        var format = new string('0', count);
                                        sheet.Column(colIndex).Style.Numberformat.Format = $"#,##0.{format}";
                                    }
                                }
                            }
                        }
                    }
                    range.Value = value;
                    colIndex++;
                }

                #region 额外列

                if (additionalColumns != null && additionalValue != null)
                {
                    foreach (var ecolumn in additionalColumns)
                    {
                        var value = additionalValue(item, ecolumn.Value);
                        var range = sheet.Cells[rowIndex, colIndex];
                        range.Value = value;
                        colIndex++;
                    }
                }

                #endregion 额外列

                rowIndex++;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();

            sheet.Hidden = hidden;

            return package;
        }

        private static void SetSheetColumnDefaultStyle(ExcelWorksheet sheet, int colIndex, Type type)
        {
            if (type == typeof(short) || type == typeof(int) || type == typeof(long)
                || type == typeof(short?) || type == typeof(int?) || type == typeof(long?))
            {
                sheet.Column(colIndex).Style.Numberformat.Format = "#,##0";
            }
            else if (type == typeof(decimal) || type == typeof(double) || type == typeof(float)
                     || type == typeof(decimal?) || type == typeof(double?) || type == typeof(float?))
            {
                sheet.Column(colIndex).Style.Numberformat.Format = "#,##0.00";
            }
            else if (type == typeof(DateTime) || type == typeof(DateTime?))
            {
                sheet.Column(colIndex).Style.Numberformat.Format = "yyyy-mm-dd";
            }
            else if (type == typeof(DateTimeOffset) || type == typeof(DateTimeOffset?))
            {
                sheet.Column(colIndex).Style.Numberformat.Format = "yyyy-m-d h:mm:ss";
            }
            else
            {
                sheet.Column(colIndex).Style.Numberformat.Format = "@";
            }
        }

        /// <summary>
        /// 数据集合转换为byte数组
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="lst"></param>
        /// <param name="columns"></param>
        /// <param name="sheetName"></param>
        /// <param name="package"></param>
        /// <param name="startRowNum"></param>
        /// <param name="startColNum"></param>
        /// <param name="hasTitle"></param>
        /// <param name="hidden"></param>
        /// <param name="convertTimeColumns"></param>
        /// <returns></returns>
        public static byte[] WriteToExcelBytes<T>(List<T> lst, Dictionary<string, string> columns, string sheetName,
       ExcelPackage? package = null, int startRowNum = 1, int startColNum = 1, bool hasTitle = true,
       eWorkSheetHidden hidden = eWorkSheetHidden.Visible, string[]? convertTimeColumns = null, bool isAutoFormatDecimal = false)
       where T : class
        {
            try
            {
                package = WriteToExcel(lst, columns, sheetName, package, startRowNum, startColNum, hasTitle, hidden, null, null,
                    convertTimeColumns ?? new[] { ConstDefinition.System.CreateTimeColumn, ConstDefinition.System.LastEditTimeColumn }, isAutoFormatDecimal);
                return package.GetAsByteArray();
            }
            finally
            {
                package?.Dispose();
            }
        }

        public static DataTable ReadTemplateFromExcelToDataTable(Stream stream, string extension = ".xlsx")
        {
            stream.Position = 0;
            try
            {
                IWorkbook workbook;
                if (extension.Equals(ConstDefinition.Common.ExtensionOfExcel, StringComparison.CurrentCultureIgnoreCase))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    workbook = new XSSFWorkbook(stream); //07版本及以上
                }


                var dt = new DataTable();
                //读取当前表数据
                ISheet sheet = workbook.GetSheetAt(0);
                //表头
                IRow header = sheet.GetRow(sheet.FirstRowNum);
                List<int> columns = new();

                for (int i = 0; i < header.LastCellNum; i++)
                {
                    object? obj = GetValueType(header.Cells[i]);
                    if (obj == null || obj.ToString()?.Trim() == string.Empty)
                    {
                        continue;
                    }
                    else
                    {
                        var objString = obj.ToString();
                        if (!string.IsNullOrEmpty(objString))
                            dt.Columns.Add(new DataColumn(objString));
                    }
                    columns.Add(i);
                }
                //数据
                for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
                {
                    int nullCount = 0;
                    var sheetRow = sheet.GetRow(i);
                    if (sheetRow == null)
                        continue;

                    DataRow dr = dt.NewRow();

                    foreach (int j in columns)
                    {
                        var cell = sheetRow.GetCell(j);
                        if (cell == null)
                        {
                            ++nullCount;
                            continue;
                        }
                        var obj = GetValueType(cell);
                        var objString = obj?.ToString();
                        if (obj == null || string.IsNullOrWhiteSpace(objString))
                            ++nullCount;

                        dr[j] = objString?.Trim();

                    }
                    //跳过空行
                    if (nullCount != columns.Count)
                        dt.Rows.Add(dr);
                }
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception("Excel文件读取失败,请检查文件是否与系统要求一致", ex);
            }
            finally
            {
                stream.Dispose();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="sheetName">读取Excel的Sheet名称</param>
        /// <param name="startIndex">开始行的索引</param>
        /// <param name="extension">扩展名</param>
        /// <param name="headerIndex">表头索引</param>
        /// <returns></returns>
        public static DataTable ReadTemplateFromExcelToDataTable(Stream stream, string sheetName, int startIndex, string extension = ".xlsx", int headerIndex = 0)
        {
            stream.Position = 0;
            try
            {
                IWorkbook workbook;
                if (extension.Equals(ConstDefinition.Common.ExtensionOfExcel, StringComparison.CurrentCultureIgnoreCase))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    workbook = new XSSFWorkbook(stream); //07版本及以上
                }

                var dt = new DataTable();
                //读取当前表数据
                ISheet? sheet = workbook.GetSheet(sheetName);
                if (sheet == null)
                    throw new Exception($"Excel中缺少{sheetName}工作表");
                    
                //表头
                IRow? header = sheet.GetRow(headerIndex);
                if (header == null)
                    throw new Exception($"Excel中第{headerIndex}行不存在");
                    
                List<int> columns = new();

                for (int i = 0; i < header.LastCellNum; i++)
                {
                    object? obj = GetValueType(header.Cells[i]);
                    if (obj == null || obj.ToString()?.Trim() == string.Empty)
                    {
                        continue;
                    }
                    else
                    {
                        var objString = obj.ToString();
                        if (!string.IsNullOrEmpty(objString))
                            dt.Columns.Add(new DataColumn(objString));
                    }
                    columns.Add(i);
                }
                //数据
                for (int i = startIndex; i <= sheet.LastRowNum; i++)
                {
                    int nullCount = 0;
                    var sheetRow = sheet.GetRow(i);
                    if (sheetRow == null)
                        continue;

                    DataRow dr = dt.NewRow();

                    foreach (int j in columns)
                    {
                        var cell = sheetRow.GetCell(j);

                        if (cell == null)
                        {
                            ++nullCount;
                            continue;
                        }

                        var obj = GetValueType(cell);
                        var objString = obj?.ToString();
                        if (obj == null || string.IsNullOrWhiteSpace(objString))
                            ++nullCount;

                        dr[j] = objString?.Trim();

                    }
                    //跳过空行
                    if (nullCount != columns.Count)
                        dt.Rows.Add(dr);
                }
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception("Excel文件读取失败,请检查文件是否与系统要求一致", ex);
            }
            finally
            {
                stream.Dispose();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="sheetName">读取Excel的Sheet名称</param>
        /// <param name="startIndex">开始行的索引</param>
        /// <param name="extension">扩展名</param>
        /// <param name="headerIndex">表头索引</param>
        /// <returns></returns>
        public static DataTable ReadTemplateFromExcelToDataTableAndKeepStream(Stream stream, string sheetName, int startIndex, string extension = ".xlsx", int headerIndex = 0)
        {
            stream.Position = 0;
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, (int)stream.Length);
            stream.Flush();

            using (MemoryStream memoryStream = new MemoryStream(bytes))
            {
                try
                {
                    //memoryStream.Position = 0;
                    IWorkbook workbook;
                    if (extension.Equals(ConstDefinition.Common.ExtensionOfExcel, StringComparison.CurrentCultureIgnoreCase))
                    {
                        workbook = new HSSFWorkbook(memoryStream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(memoryStream); //07版本及以上
                    }

                    var dt = new DataTable();
                    //读取当前表数据
                    ISheet sheet = workbook.GetSheet(sheetName);
                    if (sheet == null)
                        throw new Exception($"Excel中缺少{sheetName}工作表");

                    //表头
                    IRow header = sheet.GetRow(headerIndex);
                    List<int> columns = new();

                    for (int i = 0; i < header.LastCellNum; i++)
                    {
                        object? obj = GetValueType(header.Cells[i]);
                        if (obj == null || obj.ToString()?.Trim() == string.Empty)
                        {
                            continue;
                        }
                        else
                        {
                            var objString = obj.ToString();
                            if (!string.IsNullOrEmpty(objString))
                                dt.Columns.Add(new DataColumn(objString));
                        }
                        columns.Add(i);
                    }
                    //数据
                    for (int i = startIndex; i <= sheet.LastRowNum; i++)
                    {
                        int nullCount = 0;
                        var sheetRow = sheet.GetRow(i);
                        if (sheetRow == null)
                            continue;

                        DataRow dr = dt.NewRow();

                        foreach (int j in columns)
                        {
                            var cell = sheetRow.GetCell(j);

                            if (cell == null)
                            {
                                ++nullCount;
                                continue;
                            }

                            var obj = GetValueType(cell);
                            var objString = obj?.ToString();
                            if (obj == null || string.IsNullOrWhiteSpace(objString))
                                ++nullCount;

                            dr[j] = objString?.Trim();

                        }
                        //跳过空行
                        if (nullCount != columns.Count)
                            dt.Rows.Add(dr);
                    }
                    return dt;
                }
                catch (Exception ex)
                {
                    stream.Dispose();
                    throw new Exception("Excel文件读取失败,请检查文件是否与系统要求一致", ex);
                }
            }
        }

        private static object? GetValueType(ICell? cell)
        {
            if (cell == null)
                return null;
            CellType cellType = cell.CellType;
            if (cellType == CellType.Formula)
                cellType = cell.CachedFormulaResultType;
            switch (cellType)
            {
                case CellType.Blank: //BLANK:
                    return null;

                case CellType.Boolean: //BOOLEAN:
                    return cell.BooleanCellValue;

                case CellType.Numeric: //NUMERIC:
                    try
                    {
                        if (cell.CellType == CellType.Formula)
                        {
                            //如果是公式的话，没法判断是日期还是数字；Excel中把日期设置为字符串；
                            return cell.NumericCellValue;
                        }
                        else
                        {
                            var cellValue = cell.ToString();
                            if (!string.IsNullOrEmpty(cellValue) && IsNumeric(cellValue))
                            {
                                return cell.NumericCellValue;
                            }

                            DateTime date = cell.DateCellValue;
                            return date.ToString().Replace("0:00:00", "");
                        }
                    }
                    catch
                    {
                        return cell.NumericCellValue;
                    }

                case CellType.String: //STRING:
                    return cell.StringCellValue;

                case CellType.Error: //ERROR:
                    return cell.ErrorCellValue;

                default:
                    return "=" + cell.CellFormula;
            }
        }

        private static bool IsNumeric(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return false;
            return System.Text.RegularExpressions.Regex.IsMatch(value, @"^[+-]?\d*[.]?\d*$");
        }

        public static byte[] WriteToExcelBytes(Dictionary<string, DataTable> dicExcelData,
            ExcelPackage? package = null, int startRowNum = 1, int startColNum = 1, bool hasTitle = true,
            eWorkSheetHidden hidden = eWorkSheetHidden.Visible)
        {
            try
            {
                foreach (var curSheetData in dicExcelData)
                {
                    package = WriteToExcel(curSheetData.Value, curSheetData.Key, package, startRowNum, startColNum, hasTitle, hidden);
                }
                return package?.GetAsByteArray() ?? new byte[0];
            }
            finally
            {
                package?.Dispose();
            }

        }

        /// <summary>
        /// 读取Excel中所有sheet名和对应列名
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="extension"></param>
        /// <returns></returns>
        public static Dictionary<string, List<string>> ReadTemplateFromExcelToDic(Stream stream, string extension = ".xlsx",string? returnExcelSheetSigns = null)
        {
            Dictionary<string, List<string>> result = new Dictionary<string, List<string>>();

            stream.Position = 0;
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, (int)stream.Length);
            stream.Flush();
            using (MemoryStream memoryStream = new MemoryStream(bytes))
            {
                try
                {
                    IWorkbook workbook;
                    if (extension.Equals(ConstDefinition.Common.ExtensionOfExcel, StringComparison.CurrentCultureIgnoreCase))
                    {
                        workbook = new HSSFWorkbook(memoryStream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(memoryStream); //07版本及以上
                    }

                    var sheetCounts = workbook.NumberOfSheets;
                    ISheet? sheet = null;
                    List<string>? columnNames = null;
                    for (int i = 0; i < sheetCounts; i++)
                    {
                        columnNames = new List<string>();
                        sheet = workbook.GetSheetAt(i);

                        if (!string.IsNullOrEmpty(returnExcelSheetSigns) && !sheet.SheetName.Contains(returnExcelSheetSigns))
                        {
                            continue;
                        }


                        IRow? header = sheet.GetRow(sheet.FirstRowNum);
                        if (header != null)
                        {
                            object? obj = GetValueType(header.GetCell(0));
                            if (obj == null || obj.ToString()?.Trim() == string.Empty)
                            {
                                header = sheet.GetRow(sheet.FirstRowNum + 1);
                            }
                        }

                        if (header != null)
                        {
                            for (int j = 0; j < header.LastCellNum; j++)
                            {
                                object? objVal = GetValueType(header.Cells[j]);
                                if (objVal == null ||  objVal.ToString()?.Trim() == string.Empty)
                                {
                                    continue;
                                }
                                else
                                {
                                    var objValString = objVal.ToString();
                                    if (!string.IsNullOrEmpty(objValString))
                                        columnNames.Add(objValString);
                                }
                            }
                        }
                        result.Add(sheet.SheetName, columnNames);
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    throw new Exception("Excel文件读取失败,请检查文件是否与系统要求一致", ex);
                }
                finally
                {
                    stream.Dispose();
                }
            }
        }
    }
}
