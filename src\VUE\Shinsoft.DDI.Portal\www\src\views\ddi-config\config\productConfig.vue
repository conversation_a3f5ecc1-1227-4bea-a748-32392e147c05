<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>DDI配置与监控</el-breadcrumb-item>
        <el-breadcrumb-item>DDI配置</el-breadcrumb-item>
        <el-breadcrumb-item>产品配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-input
            v-model="filter.productCode"
            placeholder="产品编码"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.productName"
            placeholder="产品名称"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="filter.configType" placeholder="配置类型" clearable>
            <el-option label="映射规则" value="映射规则" />
            <el-option label="数据校验" value="数据校验" />
            <el-option label="转换规则" value="转换规则" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addProductConfig">新增配置</el-button>
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="productConfigList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="productCode" label="产品编码" width="120" />
        <el-table-column prop="productName" label="产品名称" min-width="180" />
        <el-table-column prop="configType" label="配置类型" width="120" />
        <el-table-column prop="configKey" label="配置项" width="150" />
        <el-table-column prop="configValue" label="配置值" min-width="200" />
        <el-table-column prop="status" label="状态" width="80" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editProductConfig(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" type="danger" @click="deleteProductConfig(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductConfig',
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        productCode: '',
        productName: '',
        configType: ''
      },
      totalCount: 0,
      productConfigList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          productCode: 'P001',
          productName: '阿司匹林肠溶片',
          configType: '映射规则',
          configKey: '通用名映射',
          configValue: 'aspirin -> 阿司匹林',
          status: '启用',
          updateTime: '2024-01-15 10:30:00'
        },
        {
          id: 2,
          productCode: 'P002',
          productName: '青霉素注射液',
          configType: '数据校验',
          configKey: '规格校验',
          configValue: '必须包含单位(mg/ml)',
          status: '启用',
          updateTime: '2024-01-16 14:20:00'
        },
        {
          id: 3,
          productCode: 'P003',
          productName: '布洛芬缓释胶囊',
          configType: '转换规则',
          configKey: '剂型转换',
          configValue: 'capsule -> 胶囊剂',
          status: '启用',
          updateTime: '2024-01-17 09:15:00'
        },
        {
          id: 4,
          productCode: 'P004',
          productName: '手术刀片',
          configType: '映射规则',
          configKey: '分类映射',
          configValue: 'medical_device -> 医疗器械',
          status: '停用',
          updateTime: '2024-01-18 16:45:00'
        },
        {
          id: 5,
          productCode: 'P005',
          productName: '医用镊子',
          configType: '数据校验',
          configKey: '型号校验',
          configValue: '必须符合标准格式',
          status: '启用',
          updateTime: '2024-01-19 11:20:00'
        }
      ]
    }
  },
  mounted() {
    this.loadProductConfigList();
  },
  methods: {
    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadProductConfigList();
    },

    // 加载产品配置列表数据
    loadProductConfigList() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.productConfigList;

        // 根据产品编码过滤
        if (this.filter.productCode) {
          filteredList = filteredList.filter(item =>
            item.productCode.includes(this.filter.productCode)
          );
        }

        // 根据产品名称过滤
        if (this.filter.productName) {
          filteredList = filteredList.filter(item =>
            item.productName.includes(this.filter.productName)
          );
        }

        // 根据配置类型过滤
        if (this.filter.configType) {
          filteredList = filteredList.filter(item =>
            item.configType === this.filter.configType
          );
        }

        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.productConfigList = filteredList.slice(start, end);

        this.loading = false;
      }, 500);
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadProductConfigList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.page = page;
      this.loadProductConfigList();
    },
    // 新增配置
    addProductConfig() {
      this.$message.info('新增配置功能开发中...');
    },
    // 编辑配置
    editProductConfig(row) {
      this.$message.info(`编辑配置：${row.productName}`);
    },
    // 删除配置
    deleteProductConfig(row) {
      this.$confirm(`确定要删除产品"${row.productName}"的配置吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    }
  }
}
</script>
