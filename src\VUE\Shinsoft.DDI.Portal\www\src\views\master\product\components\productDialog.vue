<!--
/**
 * 产品管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑产品' : '新增产品'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="productFormRef"
      :model="productForm"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="药企" prop="ManufacturerId">
            <el-select v-model="productForm.ManufacturerId" placeholder="请选择药企" style="width: 100%">
              <el-option 
                v-for="manufacturer in manufacturerList" 
                :key="manufacturer.id" 
                :label="manufacturer.name" 
                :value="manufacturer.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="中文名称" prop="NameCn">
            <el-input v-model="productForm.NameCn" placeholder="请输入中文名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="英文名称" prop="NameEn">
            <el-input v-model="productForm.NameEn" placeholder="请输入英文名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="通用名" prop="CommonName">
            <el-input v-model="productForm.CommonName" placeholder="请输入通用名" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'productDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const productFormRef = ref(null)
    
    // 保存加载状态
    const saveLoading = ref(false)
    
    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })
    
    // 表单数据
    const productForm = reactive({
      ManufacturerId: '',
      NameCn: '',
      NameEn: '',
      CommonName: ''
    })

    // 厂家列表
    const manufacturerList = ref([])

    // 表单验证规则
    const formRules = computed(() => ({
      ManufacturerId: [
        { required: true, message: '请选择药企', trigger: 'change' }
      ],
      NameCn: [
        { required: true, message: '请输入中文名称', trigger: 'blur' },
        { max: 50, message: '中文名称长度不能超过50个字符', trigger: 'blur' }
      ],
      NameEn: [
        { max: 50, message: '英文名称长度不能超过50个字符', trigger: 'blur' }
      ],
      CommonName: [
        { required: true, message: '请输入通用名', trigger: 'blur' },
        { max: 100, message: '通用名长度不能超过100个字符', trigger: 'blur' }
      ]
    }))

    /**
     * 加载厂家列表
     */
    const loadManufacturerList = () => {
      // TODO: 实际项目中需要调用API接口获取厂家列表
      // this.$http.get('/Manufacturer/GetManufacturerList').then(response => {
      //   if (response.success) {
      //     manufacturerList.value = response.data;
      //   } else {
      //     ElMessage.error(response.message || '加载厂家列表失败');
      //   }
      // });

      // 模拟厂家数据
      manufacturerList.value = [
        { id: '1', name: '拜耳医药保健有限公司' },
        { id: '2', name: '华北制药集团有限责任公司' },
        { id: '3', name: '扬子江药业集团有限公司' },
        { id: '4', name: '美敦力医疗用品技术服务有限公司' },
        { id: '5', name: '强生医疗器材有限公司' },
        { id: '6', name: '诺华制药有限公司' }
      ]
    }

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(productForm, {
        ManufacturerId: '',
        NameCn: '',
        NameEn: '',
        CommonName: ''
      })
      
      if (productFormRef.value) {
        productFormRef.value.resetFields()
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = (recordId) => {
      // TODO: 实际项目中需要调用API接口获取数据
      // this.$http.get(`/Product/GetProduct/${recordId}`).then(response => {
      //   if (response.success) {
      //     Object.assign(productForm, response.data);
      //   } else {
      //     ElMessage.error(response.message || '加载数据失败');
      //   }
      // });

      // 模拟数据加载
      console.log('加载产品数据，ID:', recordId)
      setTimeout(() => {
        const mockData = {
          ManufacturerId: '1',
          NameCn: '阿司匹林肠溶片',
          NameEn: 'Aspirin Enteric-coated Tablets',
          CommonName: '阿司匹林'
        }
        Object.assign(productForm, mockData)
      }, 100)
    }

    /**
     * 保存产品信息
     */
    const handleSave = () => {
      productFormRef.value.validate((valid) => {
        if (valid) {
          saveLoading.value = true

          // TODO: 实际项目中需要调用API接口
          // const apiUrl = isEdit.value ? '/Product/UpdateProduct' : '/Product/CreateProduct';
          // this.$http.post(apiUrl, productForm).then(response => {
          //   if (response.success) {
          //     ElMessage.success(isEdit.value ? '产品信息更新成功' : '产品添加成功');
          //     emit('success');
          //     handleClose();
          //   } else {
          //     ElMessage.error(response.message || '操作失败');
          //   }
          //   saveLoading.value = false;
          // }).catch(error => {
          //   ElMessage.error('操作失败：' + error.message);
          //   saveLoading.value = false;
          // });

          // 模拟保存操作
          setTimeout(() => {
            ElMessage.success(isEdit.value ? '产品信息更新成功' : '产品添加成功')
            saveLoading.value = false
            emit('success')
            handleClose()
          }, 1000)
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      }
    })

    // 组件挂载时加载厂家列表
    onMounted(() => {
      loadManufacturerList()
    })

    return {
      productFormRef,
      saveLoading,
      isEdit,
      dialogVisible,
      productForm,
      formRules,
      manufacturerList,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>
