<!--经销商管理页面 - 已替换为Element Plus组件，权限控制已临时注释-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>主数据管理</el-breadcrumb-item>
        <el-breadcrumb-item>收货方管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="4">
          <el-cascader
            v-model="selectedCity"
            :options="cityList"
            placeholder="省份/城市"
            clearable
            @change="handleCascaderChange"
          />
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="filter.distributorName"
            placeholder="收货方名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.gbCode"
            placeholder="编码"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-button icon="Search" @click="search">查询</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 操作按钮区域 -->
    <!-- TODO: 原有的权限控制已临时注释，正式环境需要恢复 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addDistributor">新增收货方</el-button>
        <el-button icon="Download" @click="ExportDistributorList">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="distributorList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" fixed="left" />        
        <el-table-column prop="Code" label="Code" width="70" fixed="left"  />      
        <el-table-column prop="Name" label="收货方名称" min-width="180" fixed="left" />
        <el-table-column prop="receiverType" label="收货方类型" min-width="180" fixed="left" />
        <el-table-column prop="EmployeeName" label="省份" width="70" />
        <el-table-column prop="EmployeeName" label="城市" width="70" />
        <el-table-column prop="GBCode" label="社会统一信用码" min-width="120" />
        <el-table-column prop="Address" label="地址" min-width="200" />
        <el-table-column prop="Phone" label="电话" width="120" />
        <el-table-column prop="Status" label="状态" width="120" />
        <el-table-column prop="StopTime" label="停用时间" width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editDistributor(row)" />   
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" @click="removeDistributor(row)" />   
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changeProductPageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 收货方对话框组件 -->
    <DistributorDialog
      v-model:visible="showAddDialog"
      :record-id="currentEditId"
      @success="handleDialogSuccess"
    />

  </div>
</template>

<script>
import DistributorDialog from './components/distributorDialog.vue'

export default {
  name: 'DistributorList',
  components: {
    DistributorDialog
  },
  data() {
    return {
      loading: false,
      selectedCity: [],
      pageSizeOpts: [10, 20, 50, 100],
      // 弹窗相关
      showAddDialog: false,
      currentEditId: null,
      cityList: [
        {
          value: '110000',
          label: '北京市',
          children: [{ value: '110100', label: '北京市' }]
        },
        {
          value: '440000',
          label: '广东省',
          children: [
            { value: '440100', label: '广州市' },
            { value: '440300', label: '深圳市' }
          ]
        }
      ],
      filter: {
        page: 1,
        per: 10,
        distributorName: '',
        gbCode: '',
        provinceID: '',
        cityID: ''
      },
      totalCount: 50,
      distributorList: [
        {
          ID: 1,
          Name: '北京医药有限公司',
          EmployeeName: '张三',
          Code: 'BJ001',
          GBCode: 'GB001',
          Address: '北京市朝阳区建国路1号',
          Phone: '010-12345678',
          CreateTime: '2024-01-15',
          EnumEntityStatus: 0
        },
        {
          ID: 2,
          Name: '上海康健医药公司',
          EmployeeName: '李四',
          Code: 'SH001',
          GBCode: 'GB002',
          Address: '上海市浦东新区陆家嘴路100号',
          Phone: '021-87654321',
          CreateTime: '2024-01-16',
          EnumEntityStatus: 0
        },
        {
          ID: 3,
          Name: '广州南方医药集团',
          EmployeeName: '王五',
          Code: 'GZ001',
          GBCode: 'GB003',
          Address: '广州市天河区珠江新城',
          Phone: '020-11111111',
          CreateTime: '2024-01-17',
          EnumEntityStatus: 0
        }
      ]
    };
  },
  mounted() {
    // TODO: 原有的权限初始化已临时注释，正式环境需要恢复
    // this.behaviors.behaviorsSession(localStorage.behaviors, 'distributorList');
    this.initData();
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      console.log('使用假数据初始化');
    },




    /**
     * 搜索功能
     */
    search() {
      console.log('搜索', this.filter);
    },

    /**
     * 新增收货方
     */
    addDistributor() {
      this.currentEditId = null
      this.showAddDialog = true
    },

    /**
     * 编辑收货方
     */
    editDistributor(row) {
      this.currentEditId = row.ID
      this.showAddDialog = true
    },

    /**
     * 对话框成功事件处理
     */
    handleDialogSuccess() {
      // 刷新列表数据
      this.initData()
      console.log('收货方保存成功，刷新列表')
    },

    /**
     * 级联选择器变化处理
     */
    handleCascaderChange(value) {
      if (value && value.length > 0) {
        this.filter.provinceID = value[0] || '';
        this.filter.cityID = value[1] || '';
      } else {
        this.filter.provinceID = '';
        this.filter.cityID = '';
      }
    },

    /**
     * 分页变化处理
     */
    changePage(page) {
      this.filter.page = page;
      this.search();
    },

    /**
     * 每页大小变化处理
     */
    changeProductPageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.search();
    },










    /**
     * 删除经销商
     */
    removeDistributor(row) {
      this.$confirm('确定要删除这个收货方吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 实际项目中需要调用API接口
        // this.$http.delete(`/Receiver/DeleteReceiver/${row.ID}`).then(response => {
        //   if (response.success) {
        //     this.$message.success('删除成功');
        //     this.search(); // 重新加载列表
        //   } else {
        //     this.$message.error(response.message || '删除失败');
        //   }
        // });

        // 模拟删除操作
        const index = this.distributorList.findIndex(item => item.ID === row.ID);
        if (index !== -1) {
          this.distributorList.splice(index, 1);
          this.totalCount--;
          this.$message.success('删除成功');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    /**
     * 导出经销商列表
     */
    ExportDistributorList() {
      console.log('导出收货方列表');
      // TODO: 实际项目中需要调用导出API接口
      // this.$http.get('/Receiver/ExportReceiverList', {
      //   params: this.filter,
      //   responseType: 'blob'
      // }).then(response => {
      //   const blob = new Blob([response.data]);
      //   const link = document.createElement('a');
      //   link.href = URL.createObjectURL(blob);
      //   link.download = '收货方列表.xlsx';
      //   link.click();
      // });

      this.$message.info('导出功能开发中...');
    }
  }
};
</script>
