<!--经销商管理页面 - 已替换为Element Plus组件，权限控制已临时注释-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>主数据管理</el-breadcrumb-item>
        <el-breadcrumb-item>经销商管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="4">
          <el-cascader
            v-model="selectedCity"
            :options="cityList"
            placeholder="省份/城市"
            clearable
            @change="handleCascaderChange"
          />
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="filter.distributorName"
            placeholder="商业公司名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.gbCode"
            placeholder="GB编码"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-button icon="Search" @click="search">查询</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 操作按钮区域 -->
    <!-- TODO: 原有的权限控制已临时注释，正式环境需要恢复 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addDistributorList">新增经销商</el-button>
        <el-button icon="Download" @click="ExportDistributorList">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="distributorList" stripe size="small" v-loading="loading">
        <el-table-column prop="Name" label="商业公司名称" min-width="220" fixed="left" />
        <el-table-column prop="EmployeeName" label="负责人" width="90" />
        <el-table-column prop="Code" label="外部编码" width="106" />
        <el-table-column prop="GBCode" label="GB编码" width="106" />
        <el-table-column prop="Address" label="地址" min-width="200" />
        <el-table-column prop="Phone" label="电话" width="120" />
        <el-table-column prop="CreateTime" label="创建时间" width="120" />
        <el-table-column prop="EnumEntityStatus" label="状态" width="80">
          <template #default="{ row }">
            {{ row.EnumEntityStatus === 0 ? '正常' : '停用' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editDistributor(row)" />   
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" @click="removeDistributor(row)" />   
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changeProductPageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 新增/编辑经销商弹窗 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑经销商' : '新增经销商'"
      width="800px"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
    >
      <el-form
        ref="distributorForm"
        :model="distributorForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="商业公司名称" prop="Name">
              <el-input v-model="distributorForm.Name" placeholder="请输入商业公司名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="EmployeeName">
              <el-input v-model="distributorForm.EmployeeName" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="外部编码" prop="Code">
              <el-input v-model="distributorForm.Code" placeholder="请输入外部编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="GB编码" prop="GBCode">
              <el-input v-model="distributorForm.GBCode" placeholder="请输入GB编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="Phone">
              <el-input v-model="distributorForm.Phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="省份/城市" prop="cityValue">
              <el-cascader
                v-model="distributorForm.cityValue"
                :options="cityList"
                placeholder="请选择省份/城市"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="详细地址" prop="Address">
          <el-input
            v-model="distributorForm.Address"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址"
          />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="状态" prop="EnumEntityStatus">
              <el-select v-model="distributorForm.EnumEntityStatus" placeholder="请选择状态">
                <el-option label="正常" :value="0" />
                <el-option label="停用" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveDistributor" :loading="saveLoading">
            {{ isEdit ? '更新' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DistributorList',
  data() {
    return {
      loading: false,
      saveLoading: false,
      selectedCity: [],
      pageSizeOpts: [10, 20, 50, 100],
      // 弹窗相关
      showAddDialog: false,
      isEdit: false,
      currentEditId: null,
      cityList: [
        { 
          value: '110000', 
          label: '北京市', 
          children: [{ value: '110100', label: '北京市' }] 
        },
        { 
          value: '440000', 
          label: '广东省', 
          children: [
            { value: '440100', label: '广州市' },
            { value: '440300', label: '深圳市' }
          ] 
        }
      ],
      filter: {
        page: 1,
        per: 10,
        distributorName: '',
        gbCode: '',
        provinceID: '',
        cityID: ''
      },
      totalCount: 50,
      distributorList: [
        { 
          ID: 1, 
          Name: '北京医药有限公司', 
          EmployeeName: '张三', 
          Code: 'BJ001', 
          GBCode: 'GB001', 
          Address: '北京市朝阳区建国路1号', 
          Phone: '010-12345678', 
          CreateTime: '2024-01-15', 
          EnumEntityStatus: 0 
        },
        { 
          ID: 2, 
          Name: '上海康健医药公司', 
          EmployeeName: '李四', 
          Code: 'SH001', 
          GBCode: 'GB002', 
          Address: '上海市浦东新区陆家嘴路100号', 
          Phone: '021-87654321', 
          CreateTime: '2024-01-16', 
          EnumEntityStatus: 0 
        },
        { 
          ID: 3, 
          Name: '广州南方医药集团', 
          EmployeeName: '王五', 
          Code: 'GZ001', 
          GBCode: 'GB003', 
          Address: '广州市天河区珠江新城', 
          Phone: '020-11111111', 
          CreateTime: '2024-01-17', 
          EnumEntityStatus: 0
        }
      ],
      // 表单数据
      distributorForm: {
        Name: '',
        EmployeeName: '',
        Code: '',
        GBCode: '',
        Phone: '',
        Address: '',
        cityValue: [],
        EnumEntityStatus: 0
      },
      // 表单验证规则
      formRules: {
        Name: [
          { required: true, message: '请输入商业公司名称', trigger: 'blur' }
        ],
        EmployeeName: [
          { required: true, message: '请输入负责人', trigger: 'blur' }
        ],
        Code: [
          { required: true, message: '请输入外部编码', trigger: 'blur' }
        ],
        GBCode: [
          { required: true, message: '请输入GB编码', trigger: 'blur' }
        ],
        Phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        Address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' }
        ]
      }
    };
  },
  mounted() {
    // TODO: 原有的权限初始化已临时注释，正式环境需要恢复
    // this.behaviors.behaviorsSession(localStorage.behaviors, 'distributorList');
    this.initData();
  },
  methods: {
    initData() { 
      console.log('使用假数据初始化'); 
    },
    search() { 
      console.log('搜索', this.filter); 
    },
    handleCascaderChange(value) {
      if (value && value.length > 0) {
        this.filter.provinceID = value[0] || '';
        this.filter.cityID = value[1] || '';
      } else {
        this.filter.provinceID = '';
        this.filter.cityID = '';
      }
    },
    changePage(page) {
      this.filter.page = page;
      this.search();
    },
    changeProductPageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.search();
    },
    // 新增经销商
    addDistributorList() {
      this.isEdit = false;
      this.currentEditId = null;
      this.resetForm();
      this.showAddDialog = true;
    },
    // 编辑经销商
    editDistributor(row) {
      this.isEdit = true;
      this.currentEditId = row.ID;
      this.distributorForm = {
        Name: row.Name,
        EmployeeName: row.EmployeeName,
        Code: row.Code,
        GBCode: row.GBCode,
        Phone: row.Phone,
        Address: row.Address,
        cityValue: [],
        EnumEntityStatus: row.EnumEntityStatus
      };
      this.showAddDialog = true;
    },
    // 重置表单
    resetForm() {
      this.distributorForm = {
        Name: '',
        EmployeeName: '',
        Code: '',
        GBCode: '',
        Phone: '',
        Address: '',
        cityValue: [],
        EnumEntityStatus: 0
      };
      if (this.$refs.distributorForm) {
        this.$refs.distributorForm.resetFields();
      }
    },
    // 关闭弹窗
    handleCloseDialog() {
      this.showAddDialog = false;
      this.resetForm();
    },
    // 保存经销商
    handleSaveDistributor() {
      this.$refs.distributorForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true;

          // 模拟保存操作
          setTimeout(() => {
            if (this.isEdit) {
              // 编辑模式：更新现有数据
              const index = this.distributorList.findIndex(item => item.ID === this.currentEditId);
              if (index !== -1) {
                this.distributorList[index] = {
                  ...this.distributorList[index],
                  ...this.distributorForm,
                  ID: this.currentEditId
                };
              }
              this.$message.success('经销商信息更新成功');
            } else {
              // 新增模式：添加新数据
              const newDistributor = {
                ...this.distributorForm,
                ID: Date.now(), // 使用时间戳作为临时ID
                CreateTime: new Date().toISOString().split('T')[0]
              };
              this.distributorList.unshift(newDistributor);
              this.totalCount++;
              this.$message.success('经销商添加成功');
            }

            this.saveLoading = false;
            this.handleCloseDialog();
          }, 1000);
        }
      });
    },
    ExportDistributorList() {
      console.log('导出经销商列表');
    }
  }
};
</script>

<style scoped>
/* 经销商管理页面特有样式 */

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

.el-textarea {
  width: 100%;
}

/* 操作按钮样式 */
.el-table .el-button--small {
  padding: 5px 8px;
  font-size: 12px;
}
</style>
