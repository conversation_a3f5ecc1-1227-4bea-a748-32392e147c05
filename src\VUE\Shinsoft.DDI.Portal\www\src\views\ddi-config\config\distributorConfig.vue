<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>DDI配置与监控</el-breadcrumb-item>
        <el-breadcrumb-item>DDI配置</el-breadcrumb-item>
        <el-breadcrumb-item>经销商配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="8" type="flex">
        <el-col :span="6">
          <el-input
            v-model="filter.codeOrName"
            placeholder="编号或名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="filter.status" placeholder="状态" clearable class="custom-select">
            <el-option label="正常" value="正常" />
            <el-option label="锁定" value="锁定" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.authCode"
            placeholder="授权码"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="filter.collectMethod" placeholder="采集方式" clearable class="custom-select">
            <el-option label="客户端" value="客户端" />
            <el-option label="FTP" value="FTP" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="distributorList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="code" label="编号" min-width="90" />
        <el-table-column prop="name" label="经销商名称" min-width="220" />
        <el-table-column prop="lastCollectTime" label="最新采集时间" width="160" />
        <el-table-column prop="collectMethod" label="采集方式" width="120" />
        <el-table-column prop="version" label="版本" width="180" />
        <el-table-column prop="region" label="区域" width="100" />
        <el-table-column prop="province" label="省份" width="100" />
        <el-table-column prop="city" label="城市" width="100" />
        <el-table-column prop="distributorStatus" label="状态" width="80">
          <template #default="{ row }">
            {{ row.distributorStatus === '正常' ? '正常' : row.distributorStatus }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="配置" placement="top">
                <el-button icon="Setting" circle size="small" @click="openDistributorDetail(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" @click="removeDistributor(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 经销商详细配置弹窗 -->
    <DistributorConfigDialog
      v-model:visible="showDetailDialog"
      :distributor-data="currentDistributor"
      @save="handleSaveDetail"
    />
  </div>
</template>

<script>
import DistributorConfigDialog from './components/distributorConfigDialog.vue'

export default {
  name: 'DistributorConfig',
  components: {
    DistributorConfigDialog
  },

  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      // 详细配置弹窗相关
      showDetailDialog: false,
      currentDistributor: null,
      filter: {
        page: 1,
        per: 10,
        codeOrName: '',
        status: '',
        authCode: '',
        collectMethod: ''
      },
      totalCount: 461,
      distributorList: [
        // 模拟数据，根据图片内容创建
        {
          id: 1,
          code: 'K0001',
          name: '安徽太平洋药业软件有限公司新药药房',
          lastCollectTime: '2021-01-14 11:33:49',
          collectMethod: '客户端',
          version: '2.29 (2021-01-14 11:33:43)',
          region: '东一区',
          province: '安徽',
          city: '合肥市',
          distributorStatus: '正常',
          authCode: 'AUTH001'
        },
        {
          id: 2,
          code: 'K0002',
          name: '合肥新药药大药房有限公司',
          lastCollectTime: '2020-10-11 18:03:26',
          collectMethod: '客户端',
          version: '2.28 (2020-10-11 18:02:14)',
          region: '东一区',
          province: '安徽',
          city: '合肥市',
          distributorStatus: '正常',
          authCode: 'AUTH002'
        },
        {
          id: 3,
          code: 'K0003',
          name: '常州协众大药房有限公司',
          lastCollectTime: '2019-04-24 12:07:12',
          collectMethod: 'FTP',
          version: '2.28 (2021-01-29 17:07:36)',
          region: '东二区',
          province: '江苏',
          city: '常州市',
          distributorStatus: '锁定',
          authCode: 'AUTH003'
        },
        {
          id: 4,
          code: 'K0004',
          name: '南京医药股份有限公司第一药店',
          lastCollectTime: '2024-12-03 00:04:29',
          collectMethod: '客户端',
          version: '2.28 (2024-12-03 15:04:28)',
          region: '东二区',
          province: '江苏',
          city: '南京市',
          distributorStatus: '正常',
          authCode: 'AUTH004'
        },
        {
          id: 5,
          code: 'K0005',
          name: '江苏省医药有限公司连锁药房',
          lastCollectTime: '2023-05-16 00:05:12',
          collectMethod: '客户端',
          version: '2.3 (2025-07-30 10:05:01)',
          region: '东二区',
          province: '江苏',
          city: '南京市',
          distributorStatus: '正常',
          authCode: 'AUTH005'
        },
        {
          id: 6,
          code: 'K0006',
          name: '华润河州九友医药连锁有限公司医药人员',
          lastCollectTime: '2025-01-18 04:58:22',
          collectMethod: 'FTP',
          version: '2.28 (2025-07-30 10:06:43)',
          region: '东二区',
          province: '江苏',
          city: '苏州市',
          distributorStatus: '锁定',
          authCode: 'AUTH006'
        },
        {
          id: 7,
          code: 'K0007',
          name: '徐州正康大药房有限公司',
          lastCollectTime: '2019-04-24 12:07:12',
          collectMethod: '客户端',
          version: '2.28 (2021-01-29 17:07:36)',
          region: '东二区',
          province: '江苏',
          city: '徐州市',
          distributorStatus: '正常',
          authCode: 'AUTH007'
        },
        {
          id: 8,
          code: 'K0008',
          name: '国药控股康德乐（上海）大药房有限公司',
          lastCollectTime: '2020-07-07 12:08:58',
          collectMethod: 'FTP',
          version: '2.28 (2020-07-07 15:08:12)',
          region: '东二区',
          province: '上海',
          city: '上海市',
          distributorStatus: '正常',
          authCode: 'AUTH008'
        }
      ],
      // 列映射数据
      salesColumnMapping: [
        { fieldName: 'UNIT', excelColumn: '单位', format: '', required: true },
        { fieldName: 'HOSPITAL', excelColumn: '处方医院', format: '', required: true },
        { fieldName: 'SICKNESS', excelColumn: '疾病名称', format: '', required: true },
        { fieldName: 'SUBDEALERNAME', excelColumn: '药店名称', format: '', required: true },
        { fieldName: 'PRODUCTNAME', excelColumn: '药品名称', format: '', required: true },
        { fieldName: 'SPECIFICATION', excelColumn: '规格', format: '', required: true },
        { fieldName: 'SALEDATE', excelColumn: '销售日期', format: 'yyyy/MM/dd', required: true },
        { fieldName: 'AMOUNT', excelColumn: '数量', format: '', required: true },
        { fieldName: 'BATCHNUM', excelColumn: '批号', format: '', required: true },
        { fieldName: 'VALIDITY', excelColumn: '效期', format: 'yyyy/MM/dd', required: true }
      ],
      inventoryColumnMapping: [
        { fieldName: 'UNIT', excelColumn: '单位', format: '', required: true },
        { fieldName: 'HOSPITAL', excelColumn: '处方医院', format: '', required: true },
        { fieldName: 'SICKNESS', excelColumn: '疾病名称', format: '', required: true },
        { fieldName: 'SUBDEALERNAME', excelColumn: '药店名称', format: '', required: true },
        { fieldName: 'PRODUCTNAME', excelColumn: '药品名称', format: '', required: true },
        { fieldName: 'SPECIFICATION', excelColumn: '规格', format: '', required: true },
        { fieldName: 'INVENTORYDATE', excelColumn: '库存日期', format: 'yyyy/MM/dd', required: true },
        { fieldName: 'AMOUNT', excelColumn: '数量', format: '', required: true },
        { fieldName: 'BATCHNUM', excelColumn: '批号', format: '', required: true },
        { fieldName: 'VALIDITY', excelColumn: '效期', format: 'yyyy/MM/dd', required: true }
      ],
      purchaseColumnMapping: [
        { fieldName: 'UNIT', excelColumn: '单位', format: '', required: true },
        { fieldName: 'HOSPITAL', excelColumn: '处方医院', format: '', required: true },
        { fieldName: 'SICKNESS', excelColumn: '疾病名称', format: '', required: true },
        { fieldName: 'SUBDEALERNAME', excelColumn: '药店名称', format: '', required: true },
        { fieldName: 'PRODUCTNAME', excelColumn: '药品名称', format: '', required: true },
        { fieldName: 'SPECIFICATION', excelColumn: '规格', format: '', required: true },
        { fieldName: 'PURCHASEDATE', excelColumn: '购进日期', format: 'yyyy/MM/dd', required: true },
        { fieldName: 'AMOUNT', excelColumn: '数量', format: '', required: true },
        { fieldName: 'BATCHNUM', excelColumn: '批号', format: '', required: true },
        { fieldName: 'VALIDITY', excelColumn: '效期', format: 'yyyy/MM/dd', required: true }
      ],
      // 采集记录数据
      salesCollectionRecords: [
        {
          collectDate: '2021-01-14 11:33:49',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210114113350.csv',
          totalRecords: 898,
          validRecords: 898,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-14 00:01:28',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210114000122.csv',
          totalRecords: 898,
          validRecords: 898,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-13 12:01:28',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210113120123.csv',
          totalRecords: 898,
          validRecords: 898,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-13 00:01:26',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210113000121.csv',
          totalRecords: 898,
          validRecords: 898,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-12 18:01:31',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210112180127.csv',
          totalRecords: 898,
          validRecords: 898,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-12 12:01:25',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210112120121.csv',
          totalRecords: 897,
          validRecords: 897,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-12 00:01:25',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210112000121.csv',
          totalRecords: 897,
          validRecords: 897,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-11 18:01:25',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210111180122.csv',
          totalRecords: 897,
          validRecords: 897,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-11 12:01:25',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210111120122.csv',
          totalRecords: 897,
          validRecords: 897,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-11 00:01:24',
          fileName: 'S_283b1d4f4cd084661803d91c616bb33ac_20210111000122.csv',
          totalRecords: 897,
          validRecords: 897,
          errorRecords: 0
        }
      ],
      inventoryCollectionRecords: [
        {
          collectDate: '2021-01-14 11:33:49',
          fileName: 'I_283b1d4f4cd084661803d91c616bb33ac_20210114113350.csv',
          totalRecords: 1256,
          validRecords: 1256,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-14 00:01:28',
          fileName: 'I_283b1d4f4cd084661803d91c616bb33ac_20210114000122.csv',
          totalRecords: 1256,
          validRecords: 1256,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-13 12:01:28',
          fileName: 'I_283b1d4f4cd084661803d91c616bb33ac_20210113120123.csv',
          totalRecords: 1256,
          validRecords: 1256,
          errorRecords: 0
        }
      ],
      purchaseCollectionRecords: [
        {
          collectDate: '2021-01-14 11:33:49',
          fileName: 'P_283b1d4f4cd084661803d91c616bb33ac_20210114113350.csv',
          totalRecords: 567,
          validRecords: 567,
          errorRecords: 0
        },
        {
          collectDate: '2021-01-14 00:01:28',
          fileName: 'P_283b1d4f4cd084661803d91c616bb33ac_20210114000122.csv',
          totalRecords: 567,
          validRecords: 567,
          errorRecords: 0
        }
      ],
      // 分页数据
      salesPagination: {
        page: 1,
        pageSize: 10,
        total: 2228
      },
      inventoryPagination: {
        page: 1,
        pageSize: 10,
        total: 1500
      },
      purchasePagination: {
        page: 1,
        pageSize: 10,
        total: 800
      },
      // 数据记录搜索表单
      salesSearchForm: {
        customer: '',
        distributorName: '',
        dateRange: null,
        startDate: '',
        selectAll: false,
        currentDate: false
      },
      inventorySearchForm: {
        customer: '',
        distributorName: '',
        dateRange: null,
        startDate: '',
        selectAll: false,
        currentDate: false
      },
      purchaseSearchForm: {
        customer: '',
        distributorName: '',
        dateRange: null,
        startDate: '',
        selectAll: false,
        currentDate: false
      },
      // 数据记录数据
      salesDataRecords: [],
      inventoryDataRecords: [],
      purchaseDataRecords: [],
      // 数据记录分页
      salesDataPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      inventoryDataPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      purchaseDataPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 客户端日志数据
      clientLogRecords: [
        {
          clientIp: '***************',
          logContent: '上传数据文件结束：成功(3)，失败(0)',
          clientTime: '2021-01-14 11:33:51',
          serverTime: '2021-01-14 11:33:49'
        },
        {
          clientIp: '***************',
          logContent: '开始上传数据文件',
          clientTime: '2021-01-14 11:33:51',
          serverTime: '2021-01-14 11:33:49'
        },
        {
          clientIp: '***************',
          logContent: '保存数据文件：HTTP',
          clientTime: '2021-01-14 11:33:51',
          serverTime: '2021-01-14 11:33:49'
        },
        {
          clientIp: '***************',
          logContent: '文件数据库',
          clientTime: '2021-01-14 11:33:51',
          serverTime: '2021-01-14 11:33:49'
        },
        {
          clientIp: '***************',
          logContent: '获得数据库：2行*145列',
          clientTime: '2021-01-14 11:33:51',
          serverTime: '2021-01-14 11:33:49'
        },
        {
          clientIp: '***************',
          logContent: '查询库存数据',
          clientTime: '2021-01-14 11:33:50',
          serverTime: '2021-01-14 11:33:49'
        },
        {
          clientIp: '***************',
          logContent: '获得数据库：898行*117列',
          clientTime: '2021-01-14 11:33:50',
          serverTime: '2021-01-14 11:33:48'
        },
        {
          clientIp: '***************',
          logContent: '查询实出数据',
          clientTime: '2021-01-14 11:33:50',
          serverTime: '2021-01-14 11:33:48'
        },
        {
          clientIp: '***************',
          logContent: '获得数据库：861行*142列',
          clientTime: '2021-01-14 11:33:50',
          serverTime: '2021-01-14 11:33:48'
        },
        {
          clientIp: '***************',
          logContent: '查询实出数据',
          clientTime: '2021-01-14 11:33:49',
          serverTime: '2021-01-14 11:33:47'
        }
      ],
      clientLogLoading: false,
      clientLogPagination: {
        page: 1,
        pageSize: 10,
        total: 13465
      },
      // 沟通日志表单数据
      communicationLogForm: {
        appointmentTime: '',
        communicationDate: '',
        content: ''
      },
      // 沟通日志记录数据
      communicationLogRecords: [
        {
          logTime: '2021-02-23 17:55:40',
          content: '已添加中位学校 (luyao)',
          id: 1
        },
        {
          logTime: '2020-06-09 16:16:46',
          content: '已添加安装培训品。 (luyao)',
          id: 2
        }
      ],
      // 详细配置表单数据
      detailForm: {
        // 基本配置字段
        code: '',
        name: '',
        fullName: '',
        address: '',
        customerCode1: '',
        customerCode2: '',
        customerCode3: '',
        level: '',
        batch: '',
        ruleStatus: '',
        region: '',
        status: '',
        collectMethod: '',
        fileFormat: '',
        fileDirectory: '',
        authCode: '',
        distributorContact: '',
        keyuanContact: '',

        // 客户端配置字段
        runMode: '',
        serverCheckFrequency: 60,
        currentVersion: '',
        autoUpgrade: 'false',
        targetVersion: '',
        upgradeDownloadUrl: '',
        repeatMode: '',
        runTime: '',
        restartTime: '',
        dataSourceType: '',
        dbConnectionType: '',
        dbConnectionString: '',
        dbBuyInSql: '',
        dbSellOutSql: '',
        dbInventorySql: '',
        uploadMethod: '',
        httpUploadUrl: '',
        httpUploadUsername: '',
        httpUploadPassword: '',
        logRecordMethod: '',
        logWebServiceUrl: '',
        logWebServiceUsername: '',
        logWebServicePassword: '',
        otherParameters: '',

        // 规则配置字段 - 销售
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 规则配置字段 - 库存
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 规则配置字段 - 购进
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false
      }
    }
  },
  mounted() {
    this.loadDistributorList();
  },
  methods: {
    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadDistributorList();
    },

    // 加载经销商列表数据
    loadDistributorList() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = [...this.distributorList];

        // 根据编号或名称过滤
        if (this.filter.codeOrName) {
          const searchTerm = this.filter.codeOrName.toLowerCase();
          filteredList = filteredList.filter(item =>
            item.code.toLowerCase().includes(searchTerm) ||
            item.name.toLowerCase().includes(searchTerm)
          );
        }

        // 根据状态过滤
        if (this.filter.status) {
          filteredList = filteredList.filter(item =>
            item.distributorStatus === this.filter.status
          );
        }

        // 根据授权码过滤
        if (this.filter.authCode) {
          filteredList = filteredList.filter(item => {
            // 假设授权码存储在authCode字段中
            // 如果实际数据中没有此字段，可以根据需要调整
            return item.authCode && item.authCode.includes(this.filter.authCode);
          });
        }

        // 根据采集方式过滤
        if (this.filter.collectMethod) {
          filteredList = filteredList.filter(item =>
            item.collectMethod === this.filter.collectMethod
          );
        }

        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.distributorList = filteredList.slice(start, end);

        this.loading = false;
      }, 500);
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadDistributorList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.page = page;
      this.loadDistributorList();
    },





    // 删除经销商
    removeDistributor(row) {
      this.$confirm(`确定要删除经销商"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },

    // 打开经销商详细配置
    openDistributorDetail(row) {
      this.currentDistributor = row;
      this.dialogTitle = `${row.code}-${row.name}`;
      this.activeTab = 'basic';

      // 填充表单数据
      this.detailForm = {
        // 基本配置字段
        code: row.code,
        name: row.name,
        fullName: row.name, // 假设全称与名称相同
        address: row.address || '',
        customerCode1: '',
        customerCode2: '',
        customerCode3: '',
        level: '',
        batch: '',
        ruleStatus: '科盟贸易',
        region: `${row.region}/${row.province}/${row.city}`,
        status: row.distributorStatus,
        collectMethod: row.collectMethod,
        fileFormat: 'CSV',
        fileDirectory: `283b1d4f4d0846618003d91c616bb33ac`,
        authCode: row.authCode,
        distributorContact: '',
        keyuanContact: '',

        // 客户端配置字段
        runMode: 'auto',
        serverCheckFrequency: 60,
        currentVersion: '2.28',
        autoUpgrade: 'true',
        targetVersion: '2.29',
        upgradeDownloadUrl: 'http://download.example.com/client/upgrade',
        repeatMode: 'daily',
        runTime: new Date(2023, 0, 1, 23, 0), // 23:00
        restartTime: new Date(2023, 0, 1, 3, 0), // 03:00
        dataSourceType: 'database',
        dbConnectionType: 'sqlserver',
        dbConnectionString: 'Data Source=localhost;Initial Catalog=DDI;User ID=sa;Password=******',
        dbBuyInSql: 'SELECT * FROM BuyIn WHERE Date >= @StartDate AND Date <= @EndDate',
        dbSellOutSql: 'SELECT * FROM SellOut WHERE Date >= @StartDate AND Date <= @EndDate',
        dbInventorySql: 'SELECT * FROM Inventory WHERE Date = @Date',
        uploadMethod: 'http',
        httpUploadUrl: 'http://upload.example.com/api/data',
        httpUploadUsername: 'uploader',
        httpUploadPassword: '******',
        logRecordMethod: 'webservice',
        logWebServiceUrl: 'http://log.example.com/api/log',
        logWebServiceUsername: 'logger',
        logWebServicePassword: '******',
        otherParameters: 'debug=true\nretry=3\ntimeout=300'
      };

      this.currentDistributor = row;
      this.showDetailDialog = true;
    },

    // 保存详细配置
    handleSaveDetail(data) {
      // 更新列表中的数据
      if (this.currentDistributor) {
        const index = this.distributorList.findIndex(item => item.id === this.currentDistributor.id);
        if (index !== -1) {
          this.distributorList[index] = {
            ...this.distributorList[index],
            name: data.name,
            distributorStatus: data.status,
            collectMethod: data.collectMethod,
            authCode: data.authCode
          };
        }
      }

      this.$message.success('配置保存成功');
    },

    // 编辑列映射
    editMapping(row) {
      this.$message.info(`编辑映射：${row.fieldName}`);
      // TODO: 实现编辑映射功能
    },

    // 删除列映射
    deleteMapping(index) {
      this.$confirm('确定要删除这个列映射吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 根据当前活动的选项卡删除对应的映射
        if (this.activeColumnMappingTab === 'sales') {
          this.salesColumnMapping.splice(index, 1);
        } else if (this.activeColumnMappingTab === 'inventory') {
          this.inventoryColumnMapping.splice(index, 1);
        } else if (this.activeColumnMappingTab === 'purchase') {
          this.purchaseColumnMapping.splice(index, 1);
        }
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 下载文件
    downloadFile(fileName) {
      this.$message.info(`下载文件：${fileName}`);
      // TODO: 实现文件下载功能
    },

    // 销售采集记录分页处理
    handleSalesPageSizeChange(pageSize) {
      this.salesPagination.pageSize = pageSize;
      this.salesPagination.page = 1;
      this.loadSalesCollectionRecords();
    },

    handleSalesPageChange(page) {
      this.salesPagination.page = page;
      this.loadSalesCollectionRecords();
    },

    // 库存采集记录分页处理
    handleInventoryPageSizeChange(pageSize) {
      this.inventoryPagination.pageSize = pageSize;
      this.inventoryPagination.page = 1;
      this.loadInventoryCollectionRecords();
    },

    handleInventoryPageChange(page) {
      this.inventoryPagination.page = page;
      this.loadInventoryCollectionRecords();
    },

    // 购进采集记录分页处理
    handlePurchasePageSizeChange(pageSize) {
      this.purchasePagination.pageSize = pageSize;
      this.purchasePagination.page = 1;
      this.loadPurchaseCollectionRecords();
    },

    handlePurchasePageChange(page) {
      this.purchasePagination.page = page;
      this.loadPurchaseCollectionRecords();
    },

    // 加载采集记录数据
    loadSalesCollectionRecords() {
      this.collectionLoading = true;
      // TODO: 实现销售采集记录加载
      setTimeout(() => {
        this.collectionLoading = false;
      }, 500);
    },

    loadInventoryCollectionRecords() {
      this.collectionLoading = true;
      // TODO: 实现库存采集记录加载
      setTimeout(() => {
        this.collectionLoading = false;
      }, 500);
    },

    loadPurchaseCollectionRecords() {
      this.collectionLoading = true;
      // TODO: 实现购进采集记录加载
      setTimeout(() => {
        this.collectionLoading = false;
      }, 500);
    },

    // 数据记录查询方法
    searchSalesData() {
      this.dataRecordLoading = true;
      console.log('查询销售数据:', this.salesSearchForm);
      // TODO: 实现销售数据查询
      setTimeout(() => {
        this.salesDataRecords = [];
        this.salesDataPagination.total = 0;
        this.dataRecordLoading = false;
        this.$message.success('查询完成');
      }, 1000);
    },

    searchInventoryData() {
      this.dataRecordLoading = true;
      console.log('查询库存数据:', this.inventorySearchForm);
      // TODO: 实现库存数据查询
      setTimeout(() => {
        this.inventoryDataRecords = [];
        this.inventoryDataPagination.total = 0;
        this.dataRecordLoading = false;
        this.$message.success('查询完成');
      }, 1000);
    },

    searchPurchaseData() {
      this.dataRecordLoading = true;
      console.log('查询购进数据:', this.purchaseSearchForm);
      // TODO: 实现购进数据查询
      setTimeout(() => {
        this.purchaseDataRecords = [];
        this.purchaseDataPagination.total = 0;
        this.dataRecordLoading = false;
        this.$message.success('查询完成');
      }, 1000);
    },

    // 数据记录导出方法
    exportSalesData() {
      this.$message.info('导出销售数据');
      // TODO: 实现销售数据导出
    },

    exportInventoryData() {
      this.$message.info('导出库存数据');
      // TODO: 实现库存数据导出
    },

    exportPurchaseData() {
      this.$message.info('导出购进数据');
      // TODO: 实现购进数据导出
    },

    // 数据记录分页处理
    handleSalesDataPageSizeChange(pageSize) {
      this.salesDataPagination.pageSize = pageSize;
      this.salesDataPagination.page = 1;
      this.searchSalesData();
    },

    handleSalesDataPageChange(page) {
      this.salesDataPagination.page = page;
      this.searchSalesData();
    },

    handleInventoryDataPageSizeChange(pageSize) {
      this.inventoryDataPagination.pageSize = pageSize;
      this.inventoryDataPagination.page = 1;
      this.searchInventoryData();
    },

    handleInventoryDataPageChange(page) {
      this.inventoryDataPagination.page = page;
      this.searchInventoryData();
    },

    handlePurchaseDataPageSizeChange(pageSize) {
      this.purchaseDataPagination.pageSize = pageSize;
      this.purchaseDataPagination.page = 1;
      this.searchPurchaseData();
    },

    handlePurchaseDataPageChange(page) {
      this.purchaseDataPagination.page = page;
      this.searchPurchaseData();
    },

    // 客户端日志分页处理
    handleClientLogPageSizeChange(pageSize) {
      this.clientLogPagination.pageSize = pageSize;
      this.clientLogPagination.page = 1;
      this.loadClientLogData();
    },

    handleClientLogPageChange(page) {
      this.clientLogPagination.page = page;
      this.loadClientLogData();
    },

    // 加载客户端日志数据
    loadClientLogData() {
      this.clientLogLoading = true;
      console.log('加载客户端日志数据:', {
        page: this.clientLogPagination.page,
        pageSize: this.clientLogPagination.pageSize
      });
      // TODO: 实现客户端日志数据加载
      setTimeout(() => {
        this.clientLogLoading = false;
      }, 500);
    },

    // 沟通日志相关方法
    saveCommunicationLog() {
      if (!this.communicationLogForm.content.trim()) {
        this.$message.warning('请输入沟通内容');
        return;
      }

      const newLog = {
        logTime: this.communicationLogForm.communicationDate || new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-'),
        content: this.communicationLogForm.content,
        id: Date.now()
      };

      this.communicationLogRecords.unshift(newLog);

      // 重置表单
      this.communicationLogForm = {
        appointmentTime: '',
        communicationDate: '',
        content: ''
      };

      this.$message.success('沟通日志保存成功');
    },

    topCommunicationLog(row) {
      // 找到当前记录的索引
      const index = this.communicationLogRecords.findIndex(log => log.id === row.id);
      if (index > 0) {
        // 移除当前记录
        const logToTop = this.communicationLogRecords.splice(index, 1)[0];
        // 添加到顶部
        this.communicationLogRecords.unshift(logToTop);
        this.$message.success('已置顶该沟通日志');
      } else if (index === 0) {
        this.$message.info('该日志已在顶部');
      } else {
        this.$message.error('未找到该日志记录');
      }
    },


  }
}
</script>

<style scoped>
/* 经销商配置页面特有样式 */

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

/* 操作按钮样式 */
.el-table .el-button--small {
  padding: 5px 8px;
  font-size: 12px;
}

/* 详细配置弹窗样式 */
.detail-form {
  padding: 20px;
}

.detail-form .el-form-item {
  margin-bottom: 20px;
}

.detail-form .el-input,
.detail-form .el-select {
  width: 100%;
}

.data-record-content,
.client-config-content,
.communication-log-content {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 4px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 选项卡样式 */
.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-left: 1px solid #e4e7ed;
  border-top: none;
  border-bottom: none;
  border-right: none;
}

.el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
  border-left: none;
}

/* 弹窗内容区域 */
.el-dialog__body {
  padding: 10px 20px;
}

/* 表单标签样式 */
.detail-form .el-form-item__label {
  font-weight: 500;
  color: #333;
}

/* 禁用输入框样式 */
.detail-form .el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 客户端配置页面样式 */
.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.section-title {
  margin: 0 0 20px 0;
  padding: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
}

/* 数字输入框样式 */
.detail-form .el-input-number {
  width: 100%;
}

/* 时间选择器样式 */
.detail-form .el-time-picker {
  width: 100%;
}

/* 密码输入框样式 */
.detail-form .el-input--suffix .el-input__inner {
  padding-right: 30px;
}

/* 多行文本框样式 */
.detail-form .el-textarea .el-textarea__inner {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* SQL文本框特殊样式 */
.detail-form .el-form-item:has([placeholder*="SQL"]) .el-textarea__inner {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

/* 配置分组间距 */
.config-section:last-child {
  margin-bottom: 0;
}

/* 表单项标签样式 */
.detail-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 选择框样式 */
.detail-form .el-select {
  width: 100%;
}

/* 规则配置页面样式 */
.rules-config-container {
  padding: 10px;
}

.business-tabs {
  margin-bottom: 20px;
}

.business-tabs .el-tabs__header {
  margin: 0 0 20px 0;
}

.business-tabs .el-tabs__item {
  font-weight: 500;
  font-size: 14px;
}

.rule-section {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.rule-group {
  margin-bottom: 20px;
}

.rule-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.rule-options {
  margin-bottom: 15px;
}

.rule-button {
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 13px;
  padding: 8px 16px;
}

.validation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.validation-checkbox {
  margin-right: 0;
  font-size: 13px;
}

.validation-checkbox .el-checkbox__label {
  color: #606266;
  font-weight: 500;
}

.validation-checkbox.is-checked .el-checkbox__label {
  color: #409eff;
}

/* 规则配置选项卡内容区域 */
.business-tabs .el-tab-pane {
  min-height: 400px;
}

/* 规则按钮激活状态 */
.rule-button.el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.rule-button.el-button--default {
  background-color: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

.rule-button.el-button--default:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

/* 列映射配置页面样式 */
.column-mapping-container {
  padding: 10px;
}

.mapping-tabs {
  margin-bottom: 20px;
}

.mapping-tabs .el-tabs__header {
  margin: 0 0 20px 0;
}

.mapping-tabs .el-tabs__item {
  font-weight: 500;
  font-size: 14px;
}

.mapping-section {
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

.mapping-toolbar {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-buttons .el-button {
  font-size: 12px;
  padding: 6px 12px;
}

.mapping-table {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.column-table {
  font-size: 12px;
}

.column-table .el-table__header {
  background-color: #f5f7fa;
}

.column-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}

.column-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.column-table .el-switch {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #dcdfe6;
}

/* 列映射表格行样式 */
.column-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 操作按钮样式 */
.column-table .el-button.is-circle {
  width: 28px;
  height: 28px;
  padding: 0;
  margin: 0 2px;
}

/* 列映射选项卡内容区域 */
.mapping-tabs .el-tab-pane {
  min-height: 500px;
}

/* 工具栏按钮颜色 */
.toolbar-buttons .el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
}

.toolbar-buttons .el-button--info {
  background-color: #909399;
  border-color: #909399;
}

.toolbar-buttons .el-button--success {
  background-color: #67c23a;
  border-color: #67c23a;
}

.toolbar-buttons .el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

/* 采集记录页面样式 */
.collection-record-container {
  padding: 10px;
}

.collection-tabs {
  margin-bottom: 20px;
}

.collection-tabs .el-tabs__header {
  margin: 0 0 20px 0;
}

.collection-tabs .el-tabs__item {
  font-weight: 500;
  font-size: 14px;
}

.record-section {
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

.record-table {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.collection-table {
  font-size: 12px;
}

.collection-table .el-table__header {
  background-color: #f5f7fa;
}

.collection-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}

.collection-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

/* 采集记录表格行样式 */
.collection-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 文件名链接样式 */
.collection-table .el-link {
  font-size: 12px;
  word-break: break-all;
}

/* 错误条数样式 */
.error-count {
  color: #f56c6c;
  font-weight: 600;
}

/* 分页样式 */
.record-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.record-pagination .el-pagination {
  --el-pagination-font-size: 12px;
}

/* 采集记录选项卡内容区域 */
.collection-tabs .el-tab-pane {
  min-height: 500px;
}

/* 加载状态样式 */
.collection-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 数据记录页面样式 */
.data-record-container {
  padding: 10px;
}

.data-record-tabs {
  margin-bottom: 20px;
}

.data-record-tabs .el-tabs__header {
  margin: 0 0 20px 0;
}

.data-record-tabs .el-tabs__item {
  font-weight: 500;
  font-size: 14px;
}

.data-record-section {
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.search-form .el-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 15px;
}

.search-form .el-form-item__label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.search-form .el-input,
.search-form .el-date-picker {
  font-size: 12px;
}

.search-form .el-checkbox {
  font-size: 12px;
}

.search-form .el-button {
  font-size: 12px;
  padding: 6px 12px;
}

/* 数据表格样式 */
.data-table {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.record-table {
  font-size: 12px;
}

.record-table .el-table__header {
  background-color: #f5f7fa;
}

.record-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}

.record-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

/* 数据记录表格行样式 */
.record-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 数据分页样式 */
.data-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.data-pagination .el-pagination {
  --el-pagination-font-size: 12px;
}

/* 数据记录选项卡内容区域 */
.data-record-tabs .el-tab-pane {
  min-height: 600px;
}

/* 加载状态样式 */
.record-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .search-form .el-form {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
}

/* 客户端日志页面样式 */
.client-log-container {
  padding: 20px;
}

.client-log-section {
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

/* 客户端日志表格样式 */
.client-log-table {
  font-size: 12px;
}

.client-log-table .el-table__header {
  background-color: #f5f7fa;
}

.client-log-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  text-align: center;
}

.client-log-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.client-log-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 客户端IP列样式 */
.client-log-table .el-table__body td:first-child {
  text-align: center;
  font-family: 'Courier New', monospace;
  color: #409eff;
  font-weight: 500;
}

/* 日志内容列样式 */
.client-log-table .el-table__body td:nth-child(2) {
  font-family: 'Courier New', monospace;
  color: #606266;
  line-height: 1.4;
}

/* 时间列样式 */
.client-log-table .el-table__body td:nth-child(3),
.client-log-table .el-table__body td:nth-child(4) {
  text-align: center;
  font-family: 'Courier New', monospace;
  color: #909399;
  font-size: 11px;
}

/* 客户端日志分页样式 */
.client-log-pagination {
  margin-top: 0;
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.client-log-pagination .el-pagination {
  --el-pagination-font-size: 12px;
}

/* 客户端日志加载状态样式 */
.client-log-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 客户端日志表格行间距 */
.client-log-table .el-table__body tr {
  height: 40px;
}

/* 客户端日志表格边框 */
.client-log-table {
  border: 1px solid #e4e7ed;
}

.client-log-table .el-table__inner-wrapper::before {
  display: none;
}

/* 沟通日志页面样式 */
.communication-log-container {
  padding: 20px;
}

/* 沟通日志表单样式 */
.communication-log-form {
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 20px;
  margin-bottom: 20px;
}

.communication-log-form .el-form-item__label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.communication-log-form .el-input,
.communication-log-form .el-date-picker,
.communication-log-form .el-textarea {
  font-size: 13px;
}

.communication-log-form .el-textarea__inner {
  resize: vertical;
  min-height: 80px;
}

.communication-log-form .el-button {
  font-size: 13px;
  padding: 8px 16px;
}

/* 沟通日志列表样式 */
.communication-log-list {
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

/* 沟通日志表格样式 */
.communication-log-table {
  font-size: 13px;
}

.communication-log-table .el-table__header {
  background-color: #f5f7fa;
}

.communication-log-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  text-align: center;
}

.communication-log-table .el-table__body td {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.communication-log-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 日志时间列样式 */
.communication-log-table .el-table__body td:first-child {
  text-align: center;
  font-family: 'Courier New', monospace;
  color: #909399;
  font-size: 12px;
}

/* 日志内容列样式 */
.communication-log-table .el-table__body td:nth-child(2) {
  color: #606266;
  line-height: 1.5;
  padding-left: 15px;
  padding-right: 15px;
}

/* 操作列样式 */
.communication-log-table .el-table__body td:last-child {
  text-align: center;
}

.communication-log-table .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

/* 置顶按钮样式 */
.communication-log-table .el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: #fff;
}

.communication-log-table .el-button--warning:hover {
  background-color: #ebb563;
  border-color: #ebb563;
}

/* 沟通日志表格边框 */
.communication-log-table {
  border: 1px solid #e4e7ed;
}

.communication-log-table .el-table__inner-wrapper::before {
  display: none;
}

/* 沟通日志表格行间距 */
.communication-log-table .el-table__body tr {
  height: 50px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .communication-log-form .el-col {
    margin-bottom: 10px;
  }

  .communication-log-table .el-table__body td:first-child {
    font-size: 11px;
  }
}

/* 调整下拉框文字大小，使其与文本框保持一致 */
.custom-select :deep(.el-input__wrapper) {
  font-size: 12px !important;
}

.custom-select :deep(.el-input__inner) {
  font-size: 12px !important;
}

.custom-select :deep(.el-select__placeholder) {
  font-size: 12px !important;
}

.custom-select :deep(.el-select__selected-item) {
  font-size: 12px !important;
}

/* 调整下拉选项的文字大小 */
:deep(.el-select-dropdown .el-select-dropdown__item) {
  font-size: 12px !important;
}

/* 确保下拉框输入框的文字大小 */
.custom-select :deep(.el-select .el-input .el-input__wrapper .el-input__inner) {
  font-size: 12px !important;
}
</style>
