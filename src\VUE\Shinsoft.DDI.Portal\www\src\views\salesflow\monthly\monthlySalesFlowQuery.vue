<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>流向查询</el-breadcrumb-item>
        <el-breadcrumb-item>月数据查询</el-breadcrumb-item>
        <el-breadcrumb-item>月销售流向查询</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="8" type="flex">
        <el-col :span="4">
          <el-date-picker
            v-model="filter.queryDate"
            type="month"
            placeholder="销售月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            placement="bottom-start"
            popper-class="date-picker-dropdown"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="filter.salesDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placement="bottom-start"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.senderProvinceAndCity"
            :options="cityList"
            placeholder="发货方省份/城市"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.senderCode"
            placeholder="发货方Code"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.senderName"
            placeholder="发货方名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.receiverProvinceAndCity"
            :options="cityList"
            placeholder="收货方省份/城市"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.receiverCode"
            placeholder="收货方Code"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.receiverName"
            placeholder="收货方名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.productCascader"
            :options="productOptions"
            :props="cascaderProps"
            placeholder="产品名称/规格"
            clearable
            filterable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="monthlyFlowList"
        border
        stripe
        size="small"
        v-loading="loading"
        element-loading-text="数据加载中..."
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" :index="getTableIndex" />
        <el-table-column prop="salesDate" label="销售日期" width="100" align="center" fixed="left" />
        <el-table-column prop="senderName" label="发货方名称" width="200" align="left" show-overflow-tooltip fixed="left" />
        <el-table-column prop="receiverName" label="收货方名称" width="200" align="left" show-overflow-tooltip fixed="left" />
        <el-table-column prop="productName" label="产品名称" width="100" align="center" show-overflow-tooltip fixed="left" />
        <el-table-column prop="productSpec" label="产品规格" width="110" align="center" fixed="left" />
        <el-table-column prop="queryDate" label="记录月份" width="100" align="center" />
        <el-table-column prop="senderProvince" label="发货方省份" width="120" align="center" />
        <el-table-column prop="senderCity" label="发货方城市" width="120" align="center" />
        <el-table-column prop="senderCode" label="发货方Code" width="150" align="center" />
        <el-table-column prop="receiverProvince" label="收货方省份" width="120" align="center" />
        <el-table-column prop="receiverCity" label="收货方城市" width="120" align="center" />
        <el-table-column prop="receiverCode" label="收货方Code" width="150" align="center" />
        <el-table-column prop="productBatch" label="批号" width="120" align="center" />
        <el-table-column prop="quantity" label="数量" width="100" align="center" />
      </el-table>
    </div>
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MonthlySalesFlowQuery',
  data() {
    return {
      loading: false,
      filter: {
        page: 1,
        per: 10,
        queryDate: '',
        salesDateRange: [],
        senderProvinceAndCity: [],
        senderCode: '',
        senderName: '',
        receiverProvinceAndCity: [],
        receiverCode: '',
        receiverName: '',
        productCode: '',
        productCascader: [], // 产品级联选择器值
        productName: '',
        specification: '',
        manufacturer: '',
        batchNo: '',
        expiryDate: '',
        approvalNumber: '',
        dosageForm: '',
        packingSpec: '',
        unitPrice: '',
        quantity: '',
        amount: ''
      },
      // 城市级联数据
      cityList: [
        {
          value: 'beijing',
          label: '北京市',
          children: [
            { value: 'dongcheng', label: '东城区' },
            { value: 'xicheng', label: '西城区' },
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'fengtai', label: '丰台区' }
          ]
        },
        {
          value: 'shanghai',
          label: '上海市',
          children: [
            { value: 'huangpu', label: '黄浦区' },
            { value: 'xuhui', label: '徐汇区' },
            { value: 'changning', label: '长宁区' },
            { value: 'jingan', label: '静安区' }
          ]
        },
        {
          value: 'guangdong',
          label: '广东省',
          children: [
            { value: 'guangzhou', label: '广州市' },
            { value: 'shenzhen', label: '深圳市' },
            { value: 'dongguan', label: '东莞市' },
            { value: 'foshan', label: '佛山市' }
          ]
        }
      ],
      // 产品级联选择器配置
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        expandTrigger: 'hover'
      },
      // 产品选项数据
      productOptions: [
        {
          value: 'amoxicillin',
          label: '阿莫西林胶囊',
          children: [
            { value: '0.25g*24', label: '0.25g*24粒' },
            { value: '0.5g*12', label: '0.5g*12粒' }
          ]
        },
        {
          value: 'cephalexin',
          label: '头孢氨苄胶囊',
          children: [
            { value: '0.25g*24', label: '0.25g*24粒' },
            { value: '0.5g*12', label: '0.5g*12粒' }
          ]
        },
        {
          value: 'azithromycin',
          label: '阿奇霉素片',
          children: [
            { value: '0.25g*6', label: '0.25g*6片' },
            { value: '0.5g*3', label: '0.5g*3片' }
          ]
        }
      ],
      totalCount: 0,
      monthlyFlowList: []
    }
  },
  computed: {
    // 获取选中的产品名称
    selectedProductName() {
      if (this.filter.productCascader && this.filter.productCascader.length > 0) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        return productOption ? productOption.label : ''
      }
      return ''
    },
    // 获取选中的产品规格
    selectedProductSpec() {
      if (this.filter.productCascader && this.filter.productCascader.length > 1) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        if (productOption && productOption.children) {
          const specOption = productOption.children.find(item => item.value === this.filter.productCascader[1])
          return specOption ? specOption.label : ''
        }
      }
      return ''
    }
  },
  mounted() {
    // 初始化时显示所有数据，不设置默认日期过滤
    this.search();
  },
  methods: {
    // 计算表格序号（考虑分页）
    getTableIndex(index) {
      return (this.filter.page - 1) * this.filter.per + index + 1
    },

    // 查询
    search() {
      this.filter.page = 1;
      this.loadData();
    },
    
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },

    // 加载数据
    loadData() {
      this.loading = true;
      // 模拟API调用
      setTimeout(() => {
        // 这里应该调用实际的API
        this.monthlyFlowList = this.getFilteredData();
        this.totalCount = this.monthlyFlowList.length;
        this.loading = false;
      }, 500);
    },

    // 获取过滤后的数据
    getFilteredData() {
      // 模拟数据，实际使用时需要从API获取
      let mockData = [
        {
          id: 1,
          salesDate: '2024-01-15',
          senderName: '华北制药集团',
          receiverName: '北京同仁堂',
          productName: '阿莫西林胶囊',
          productSpec: '0.25g*24粒',
          queryDate: '2024-01',
          senderProvince: '河北省',
          senderCity: '石家庄市',
          senderCode: 'HBZJ001',
          receiverProvince: '北京市',
          receiverCity: '东城区',
          receiverCode: 'BJTR001',
          productBatch: 'B20240101',
          quantity: 1000,
          orderNumber: 'ORD20240115001',
          remark: '正常销售'
        },
        {
          id: 2,
          salesDate: '2024-01-16',
          senderName: '石药集团',
          receiverName: '上海医药公司',
          productName: '头孢氨苄胶囊',
          productSpec: '0.25g*24粒',
          queryDate: '2024-01',
          senderProvince: '河北省',
          senderCity: '石家庄市',
          senderCode: 'SYZY001',
          receiverProvince: '上海市',
          receiverCity: '黄浦区',
          receiverCode: 'SHYY001',
          productBatch: 'B20240102',
          quantity: 500,
          orderNumber: 'ORD20240116001',
          remark: '紧急订单'
        },
        {
          id: 3,
          salesDate: '2024-01-17',
          senderName: '扬子江药业集团',
          receiverName: '广州医药有限公司',
          productName: '阿奇霉素片',
          productSpec: '0.25g*6片',
          queryDate: '2024-01',
          senderProvince: '江苏省',
          senderCity: '泰州市',
          senderCode: 'YZJY001',
          receiverProvince: '广东省',
          receiverCity: '广州市',
          receiverCode: 'GZYY001',
          productBatch: 'B20240103',
          quantity: 800,
          orderNumber: 'ORD20240117001',
          remark: '月度采购'
        }
      ];

      // 应用过滤条件
      let filteredData = mockData;
      
      // 根据查询月份过滤
      if (this.filter.queryDate) {
        filteredData = filteredData.filter(item =>
          item.queryDate === this.filter.queryDate
        );
      }

      // 根据收货方名称过滤
      if (this.filter.receiverName) {
        filteredData = filteredData.filter(item =>
          item.receiverName.includes(this.filter.receiverName)
        );
      }

      // 根据发货方名称过滤
      if (this.filter.senderName) {
        filteredData = filteredData.filter(item =>
          item.senderName.includes(this.filter.senderName)
        );
      }

      // 根据发货方Code过滤
      if (this.filter.senderCode) {
        filteredData = filteredData.filter(item =>
          item.senderCode.includes(this.filter.senderCode)
        );
      }

      // 根据收货方Code过滤
      if (this.filter.receiverCode) {
        filteredData = filteredData.filter(item =>
          item.receiverCode.includes(this.filter.receiverCode)
        );
      }
      
      // 根据产品级联选择器过滤
      if (this.filter.productCascader && this.filter.productCascader.length > 0) {
        const productName = this.selectedProductName;
        const productSpec = this.selectedProductSpec;

        filteredData = filteredData.filter(item => {
          let match = true;
          if (productName && item.productName) {
            match = match && item.productName.includes(productName);
          }
          if (productSpec && item.productSpec) {
            match = match && item.productSpec.includes(productSpec);
          }
          return match;
        });
      }

      return filteredData;
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.filter.per = val;
      this.loadData();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.filter.page = val;
      this.loadData();
    }
  }
}
</script>
