﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>Database.Biz</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{a3190ae6-03ce-4ffd-b50a-f02322470193}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql120DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath />
    <RootNamespace>Database.Biz</RootNamespace>
    <AssemblyName>Database.Biz</AssemblyName>
    <ModelCollation>2052,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <TargetFrameworkProfile />
    <DefaultCollation>Chinese_PRC_CI_AS</DefaultCollation>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo" />
    <Folder Include="dbo\Tables" />
    <Folder Include="dbo\Views" />
    <Folder Include="Scripts" />
    <Folder Include="dbo\Stored Procedures" />
    <Folder Include="Security" />
    <Folder Include="Scripts\init" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\Attachment.sql" />
    <Build Include="dbo\Tables\Company.sql" />
    <Build Include="dbo\Tables\CompanyCfg.sql" />
    <Build Include="dbo\Tables\CompanySetting.sql" />
    <Build Include="dbo\Tables\Department.sql" />
    <Build Include="dbo\Tables\Dict.sql" />
    <Build Include="dbo\Tables\Employee.sql" />
    <Build Include="dbo\Tables\EmployeeStation.sql" />
    <Build Include="dbo\Tables\I18n.sql" />
    <Build Include="dbo\Tables\Auth.sql" />
    <Build Include="dbo\Tables\Position.sql" />
    <Build Include="dbo\Tables\Role.sql" />
    <Build Include="dbo\Tables\RoleMember.sql" />
    <Build Include="dbo\Tables\RoleAuth.sql" />
    <Build Include="dbo\Tables\RoleAuthTag.sql" />
    <Build Include="dbo\Tables\SerialNumber.sql" />
    <Build Include="dbo\Tables\Station.sql" />
    <Build Include="dbo\Tables\SysSetting.sql" />
    <Build Include="dbo\Tables\User.sql" />
    <Build Include="dbo\Views\VwRoleMember.sql" />
    <Build Include="dbo\Tables\CompanyAuth.sql" />
    <Build Include="dbo\Tables\CompanyCulture.sql" />
    <Build Include="dbo\Tables\SysCulture.sql" />
    <Build Include="dbo\Tables\DepartmentCostCenter.sql" />
    <Build Include="dbo\Stored Procedures\sp_GetSerialNumber.sql" />
    <Build Include="dbo\Views\vDepartmentCostCenter.sql" />
    <Build Include="dbo\Tables\EmployeeDelegate.sql" />
    <Build Include="dbo\Tables\ReviewData.sql" />
    <Build Include="dbo\Tables\ReviewExtInfo.sql" />
    <Build Include="dbo\Tables\ReviewIndex.sql" />
    <Build Include="dbo\Views\vCompanyAuth.sql" />
    <Build Include="dbo\Views\vAuth.sql" />
    <Build Include="dbo\Views\vRoleAuth.sql" />
    <Build Include="dbo\Tables\SyncTask.sql" />
    <Build Include="dbo\Tables\CompanyCurrency.sql" />
    <Build Include="dbo\Tables\BizMail.sql" />
    <Build Include="dbo\Views\vEmployeeDelegate.sql" />
    <Build Include="dbo\Stored Procedures\usp_AddAuth.sql" />
    <Build Include="dbo\Stored Procedures\usp_AddRoleAuth.sql" />
    <Build Include="dbo\Stored Procedures\usp_InitCompanyAuth.sql" />
    <Build Include="dbo\Views\vSerialNumber.sql" />
    <Build Include="dbo\Views\vStationLeader.sql" />
    <Build Include="dbo\Views\VwEmployeeStation.sql" />
    <Build Include="dbo\Views\VwStation.sql" />
    <Build Include="dbo\Views\VwStationLeader.sql" />
    <Build Include="dbo\Tables\Calendar.sql" />
    <Build Include="dbo\Tables\AuthTag.sql" />
    <Build Include="dbo\Tables\ReviewAuditor.sql" />
    <Build Include="dbo\Tables\ReviewTask.sql" />
    <Build Include="dbo\Tables\SerialSeed.sql" />
    <None Include="Scripts\init\10.Auth.sql" />
    <Build Include="dbo\Stored Procedures\usp_InitRoleAuth.sql" />
    <Build Include="dbo\Views\VwOrganization.sql" />
    <Build Include="dbo\Views\vOrganization.sql" />
    <Build Include="dbo\Tables\Announcement.sql" />
    <Build Include="dbo\Tables\AnnouncementContent.sql" />
    <Build Include="dbo\Tables\EmployeeDelegateAuth.sql" />
    <Build Include="dbo\Tables\EmployeeDelegateAuthTag.sql" />
    <Build Include="dbo\Tables\I18nCulture.sql" />
    <Build Include="dbo\Tables\VueRoute.sql" />
    <Build Include="dbo\Tables\VueRouteMeta.sql" />
    <Build Include="dbo\Tables\BizGroup.sql" />
    <Build Include="dbo\Tables\BizGroupMember.sql" />
    <Build Include="dbo\Tables\SubCompany.sql" />
    <Build Include="dbo\Views\vI18n.sql" />
    <Build Include="dbo\Tables\CostCenter.sql" />
    <Build Include="dbo\Tables\Receiver.sql" />
    <Build Include="dbo\Tables\ReceiverClient.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="xl.dev.publish.xml" />
  </ItemGroup>
</Project>