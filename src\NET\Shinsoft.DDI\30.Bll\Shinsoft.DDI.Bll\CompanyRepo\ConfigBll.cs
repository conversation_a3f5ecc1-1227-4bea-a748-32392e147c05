﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class ConfigBll : BaseCompanyBll
    {
        #region Constructs

        public ConfigBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public ConfigBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public ConfigBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public ConfigBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region ReceiverClient

        public BizResult DeleteBizReceiverClient(Guid id)
        {
            var cc = this.CompanyCache;

            var result = new BizResult<ReceiverClient>();

            var dbEntity = this.Get<ReceiverClient>(id);

            if (dbEntity == null)
            {
                result.Error("经销商配置不存在");
            }

            if (result.Success)
            {
                dbEntity = dbEntity.Value();

                //this.Delete(dbEntity, false);

                this.SaveChanges();

                cc.RemoveCache<ReceiverClient>();
            }

            return result;
        }
        #endregion
    }
}
