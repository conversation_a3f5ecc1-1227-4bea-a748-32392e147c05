﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class ConfigBll : BaseCompanyBll
    {
        #region Constructs

        public ConfigBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public ConfigBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public ConfigBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public ConfigBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region ReceiverClient


        #endregion
    }
}
