<!--货主管理页面 - 基于Element Plus组件-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>主数据管理</el-breadcrumb-item>
        <el-breadcrumb-item>货主管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-input
            v-model="filter.shipperName"
            placeholder="货主名称"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.shipperCode"
            placeholder="货主编码"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addShipper">新增货主</el-button>
        <el-button icon="Download" @click="exportShipperList">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="shipperList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="shipperCode" label="货主编码" width="120" />
        <el-table-column prop="shipperName" label="货主名称" min-width="200" />
        <el-table-column prop="shortName" label="简称" width="100" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="contact" label="联系人" width="100" />
        <el-table-column prop="phone" label="电话" width="130" />
        <el-table-column prop="status" label="状态" width="80" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editShipper(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" @click="removeShipper(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 货主对话框组件 -->
    <ShipperDialog
      v-model:visible="showAddDialog"
      :record-id="currentEditId"
      @success="handleDialogSuccess"
    />

  </div>
</template>

<script>
import ShipperDialog from './components/ShipperDialog.vue'

export default {
  name: 'ShipperList',
  components: {
    ShipperDialog
  },
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      // 对话框相关
      showAddDialog: false,
      currentEditId: null,
      filter: {
        page: 1,
        per: 10,
        shipperName: '',
        shipperCode: ''
      },
      totalCount: 0,
      shipperList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          shipperCode: 'SH001',
          shipperName: '北京医药货主有限公司',
          shortName: '北京医药',
          address: '北京市朝阳区建国门外大街1号',
          contact: '张经理',
          phone: '010-85123456',
          status: '启用',
          createTime: '2024-01-15'
        },
        {
          id: 2,
          shipperCode: 'SH002',
          shipperName: '上海康健货主公司',
          shortName: '上海康健',
          address: '上海市浦东新区陆家嘴环路1000号',
          contact: '李主任',
          phone: '021-68901234',
          status: '启用',
          createTime: '2024-01-16'
        },
        {
          id: 3,
          shipperCode: 'SH003',
          shipperName: '广州南方货主集团',
          shortName: '广州南方',
          address: '广州市天河区珠江新城花城大道85号',
          contact: '王总监',
          phone: '020-38765432',
          status: '停用',
          createTime: '2024-01-17'
        },
        {
          id: 4,
          shipperCode: 'SH004',
          shipperName: '深圳华润货主企业',
          shortName: '深圳华润',
          address: '深圳市南山区深南大道9678号',
          contact: '陈经理',
          phone: '0755-26888999',
          status: '启用',
          createTime: '2024-01-18'
        },
        {
          id: 5,
          shipperCode: 'SH005',
          shipperName: '天津同仁堂货主',
          shortName: '同仁堂',
          address: '天津市和平区南京路108号',
          contact: '刘药师',
          phone: '022-27654321',
          status: '启用',
          createTime: '2024-01-19'
        },
        {
          id: 6,
          shipperCode: 'SH006',
          shipperName: '杭州海正货主公司',
          shortName: '杭州海正',
          address: '杭州市滨江区江南大道3688号',
          contact: '周主管',
          phone: '0571-87123456',
          status: '停用',
          createTime: '2024-01-20'
        },
        {
          id: 7,
          shipperCode: 'SH007',
          shipperName: 'Sperogenix Therapeutics Limited',
          shortName: '曙方医药',
          address: '上海市张江高科技园区祖冲之路2288号',
          contact: 'Dr. Smith',
          phone: '021-50987654',
          status: '启用',
          createTime: '2024-01-21'
        }
      ]
    };
  },
  mounted() {
    this.loadShipperList();
  },
  methods: {
    /**
     * 加载货主列表数据
     */
    loadShipperList() {
      this.loading = true;
      
      // TODO: 实际项目中需要调用API接口
      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.shipperList;
        
        if (this.filter.shipperName) {
          filteredList = filteredList.filter(item => 
            item.shipperName.includes(this.filter.shipperName)
          );
        }
        
        if (this.filter.shipperCode) {
          filteredList = filteredList.filter(item => 
            item.shipperCode.includes(this.filter.shipperCode)
          );
        }
        
        // 按创建时间倒序排序
        filteredList.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
        
        this.totalCount = filteredList.length;
        this.loading = false;
      }, 500);
    },

    /**
     * 查询按钮点击事件
     */
    search() {
      this.filter.page = 1;
      this.loadShipperList();
    },

    /**
     * 分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadShipperList();
    },

    /**
     * 页码改变事件
     * @param {number} page - 新的页码
     */
    changePage(page) {
      this.filter.page = page;
      this.loadShipperList();
    },

    /**
     * 新增货主
     */
    addShipper() {
      this.currentEditId = null
      this.showAddDialog = true
    },

    /**
     * 编辑货主
     * @param {Object} row - 货主数据行
     */
    editShipper(row) {
      this.currentEditId = row.id
      this.showAddDialog = true
    },

    /**
     * 对话框成功事件处理
     */
    handleDialogSuccess() {
      // 刷新列表数据
      this.loadShipperList()
      console.log('货主保存成功，刷新列表')
    },

    /**
     * 删除货主
     * @param {Object} row - 货主数据行
     */
    removeShipper(row) {
      console.log('删除货主', row);
      // TODO: 实现删除货主功能
    },

    /**
     * 导出货主列表
     */
    exportShipperList() {
      console.log('导出货主列表');
      // TODO: 实现导出功能
    }
  }
};
</script>
