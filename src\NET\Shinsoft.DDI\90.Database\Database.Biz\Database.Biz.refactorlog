﻿<?xml version="1.0" encoding="utf-8"?>
<Operations Version="1.0" xmlns="http://schemas.microsoft.com/sqlserver/dac/Serialization/2012/02">
  <Operation Name="Rename Refactor" Key="71e314ee-51bc-4101-94c7-7795634171c3" ChangeDateTime="07/30/2022 15:15:50">
    <Property Name="ElementName" Value="[dbo].[PR].[PurchaseCatalogType]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[PR]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="PrPurchaseCatalogType" />
  </Operation>
  <Operation Name="Rename Refactor" Key="1f8c41a2-3e06-43e1-afb9-727dbd62347f" ChangeDateTime="07/30/2022 15:22:24">
    <Property Name="ElementName" Value="[dbo].[PR].[PrPurchaseCatalogType]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[PR]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="EnumPrPurchaseCatalogType" />
  </Operation>
  <Operation Name="Rename Refactor" Key="7b5c6b39-051d-44bc-a923-ed7b277bcac2" ChangeDateTime="07/30/2022 16:23:01">
    <Property Name="ElementName" Value="[dbo].[PR].[EnumPrPurchaseCatalogType]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[PR]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="EnumPurchaseType" />
  </Operation>
  <Operation Name="Rename Refactor" Key="70eebc04-8010-4ed9-8ccc-b3d94656da18" ChangeDateTime="08/01/2025 14:43:50">
    <Property Name="ElementName" Value="[dbo].[Receiver].[ShortName]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[Receiver]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="ReceiverTypeId" />
  </Operation>
</Operations>