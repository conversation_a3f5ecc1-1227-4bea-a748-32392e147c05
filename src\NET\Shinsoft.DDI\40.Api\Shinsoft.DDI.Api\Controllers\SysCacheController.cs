﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "缓存")]
    public class SysCacheController : BaseApiController<SysBll>
    {
        [HttpGet]
        [LogApi(ApiType.Cache, Operate = "获取系统缓存所有键")]
        [Auth(AuthCodes.Platform.Setup.SysCache_Operate)]
        public List<string> GetAllKeys()
        {
            return this.SysCache.GetAllKeys();
        }

        [HttpGet]
        [LogApi(ApiType.Cache, Operate = "销毁系统缓存")]
        [Auth(AuthCodes.Platform.Setup.SysCache_Operate)]
        public void FlushCache([Required] string key)
        {
            this.SysCache.RemoveKey(key);
        }

        [HttpGet]
        [LogApi(ApiType.Cache, Operate = "获取在线用户")]
        [Auth(AuthCodes.Platform.Setup.SysCache_Operate)]
        public List<IdentityUser> GetOnlineUsers()
        {
            var users = this.UserProvider.OnlineUsers;

            var models = users.Maps<IdentityUser>();

            return models;
        }
    }
}
