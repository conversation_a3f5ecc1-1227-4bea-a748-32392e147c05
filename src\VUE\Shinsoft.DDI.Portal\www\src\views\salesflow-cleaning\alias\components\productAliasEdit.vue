<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑产品别名' : '新增产品别名'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      size="default"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="产品编码:" prop="productCode">
            <el-input
              v-model="formData.productCode"
              placeholder="请输入产品编码"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="产品名称:" prop="productName">
            <el-input
              v-model="formData.productName"
              placeholder="请输入产品名称"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="产品规格:" prop="productSpec">
            <el-input
              v-model="formData.productSpec"
              placeholder="请输入产品规格"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="通用名:" prop="genericName">
            <el-input
              v-model="formData.genericName"
              placeholder="请输入通用名"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="产品别名:" prop="productAlias">
            <el-input
              v-model="formData.productAlias"
              placeholder="请输入产品别名"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="产品规格别名:" prop="productSpecAlias">
            <el-input
              v-model="formData.productSpecAlias"
              placeholder="请输入产品规格别名"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="状态:" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="启用" value="启用" />
              <el-option label="禁用" value="禁用" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'ProductAliasEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'success'],
  data() {
    return {
      loading: false,
      formData: {
        id: null,
        productCode: '',
        productName: '',
        productSpec: '',
        genericName: '',
        productAlias: '',
        productSpecAlias: '',
        status: '启用'
      },
      rules: {
        productCode: [
          { required: true, message: '请输入产品编码', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        productName: [
          { required: true, message: '请输入产品名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        productSpec: [
          { required: true, message: '请输入产品规格', trigger: 'blur' }
        ],
        genericName: [
          { required: true, message: '请输入通用名', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        productAlias: [
          { required: true, message: '请输入产品别名', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        productSpecAlias: [
          { required: true, message: '请输入产品规格别名', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    isEdit() {
      return this.editData && this.editData.id;
    }
  },
  watch: {
    visible(newVal) {
      console.log(newVal);
      if (newVal) {
        this.initFormData();
      } else {
        this.resetForm();
      }
    },
    editData: {
      handler() {
        if (this.visible) {
          this.initFormData();
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.isEdit) {
        // 编辑模式，填充现有数据
        this.formData = {
          id: this.editData.id,
          productCode: this.editData.productCode || '',
          productName: this.editData.productName || '',
          productSpec: this.editData.productSpec || '',
          genericName: this.editData.genericName || '',
          productAlias: this.editData.productAlias || '',
          productSpecAlias: this.editData.productSpecAlias || '',
          status: this.editData.status || '启用'
        };
      } else {
        // 新增模式，重置表单
        this.resetForm();
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        productCode: '',
        productName: '',
        productSpec: '',
        genericName: '',
        productAlias: '',
        productSpecAlias: '',
        status: '启用'
      };
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true;
          
          // 模拟API调用
          setTimeout(() => {
            const currentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
            
            const submitData = {
              ...this.formData,
              lastModifyTime: currentTime
            };
            
            if (!this.isEdit) {
              // 新增模式，设置创建时间
              submitData.createTime = currentTime;
              submitData.id = Date.now(); // 模拟生成ID
            }
            
            this.loading = false;
            this.$message.success(this.isEdit ? '更新成功' : '保存成功');
            this.$emit('success', submitData);
            this.handleClose();
          }, 1000);
          
          // 真实API调用示例（注释掉的代码）
          /*
          const apiUrl = this.isEdit ? '/ProductAlias/Update' : '/ProductAlias/Create';
          this.$http.post(apiUrl, this.formData)
            .then((response) => {
              if (response.data && response.data.success) {
                this.loading = false;
                this.$message.success(this.isEdit ? '更新成功' : '保存成功');
                this.$emit('success', response.data.data);
                this.handleClose();
              } else {
                this.loading = false;
                const message = response.data?.message || '操作失败';
                this.$message.error(message);
              }
            })
            .catch((error) => {
              this.loading = false;
              this.$message.error('操作失败');
            });
          */
        } else {
          this.$message.error('请检查表单输入');
        }
      });
    },
    
    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false);
    }
  }
}
</script>

<style scoped>
/* 禁用状态的输入框样式 */
.el-input.is-disabled .el-input__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
</style>
