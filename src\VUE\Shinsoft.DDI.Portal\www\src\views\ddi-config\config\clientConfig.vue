<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>DDI配置与监控</el-breadcrumb-item>
        <el-breadcrumb-item>DDI配置</el-breadcrumb-item>
        <el-breadcrumb-item>客户端配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-input
            v-model="filter.clientCode"
            placeholder="客户端编码"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.clientName"
            placeholder="客户端名称"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="filter.clientType" placeholder="客户端类型" clearable>
            <el-option label="Web客户端" value="Web客户端" />
            <el-option label="移动客户端" value="移动客户端" />
            <el-option label="API客户端" value="API客户端" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addClientConfig">新增配置</el-button>
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="clientConfigList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" fixed="left" />
        <el-table-column prop="code" label="编号" width="100" fixed="left" />
        <el-table-column prop="distributorName" label="经销商名称" min-width="180" fixed="left" />
        <el-table-column prop="clientCode" label="客户端编号" width="120" fixed="left" />
        <el-table-column prop="clientName" label="客户端名称" min-width="150" fixed="left" />
        <el-table-column prop="clientType" label="客户端类型" width="120" />
        <el-table-column prop="version" label="版本号" width="100" />
        <el-table-column prop="detectFrequency" label="检测频率" width="120" />
        <el-table-column prop="repeatMethod" label="重复方式" width="120" />
        <el-table-column prop="dataSourceMethod" label="数据源获取方式" width="140" />
        <el-table-column prop="targetUploadMethod" label="目标上传方式" width="140" />
        <el-table-column prop="runTime" label="运行时间" width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editClientConfig(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" type="danger" @click="deleteClientConfig(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ClientConfig',
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        clientCode: '',
        clientName: '',
        clientType: ''
      },
      totalCount: 0,
      clientConfigList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          code: 'K0001',
          distributorName: '安徽太平洋药业软件有限公司新药药房',
          clientCode: 'C001',
          clientName: 'DDI管理平台',
          clientType: 'Web客户端',
          version: 'v1.2.0',
          detectFrequency: '每小时',
          repeatMethod: '覆盖',
          dataSourceMethod: '数据库直连',
          targetUploadMethod: 'HTTP上传',
          runTime: '2024-01-15 10:30:00',
          apiUrl: 'https://api.ddi.com/v1',
          status: '启用',
          updateTime: '2024-01-15 10:30:00'
        },
        {
          id: 2,
          code: 'K0002',
          distributorName: '合肥新药药大药房有限公司',
          clientCode: 'C002',
          clientName: 'DDI移动端',
          clientType: '移动客户端',
          version: 'v2.1.5',
          detectFrequency: '每30分钟',
          repeatMethod: '追加',
          dataSourceMethod: 'FTP获取',
          targetUploadMethod: 'SFTP上传',
          runTime: '2024-01-16 14:20:00',
          apiUrl: 'https://mobile-api.ddi.com/v2',
          status: '启用',
          updateTime: '2024-01-16 14:20:00'
        },
        {
          id: 3,
          code: 'K0003',
          distributorName: '常州协众大药房有限公司',
          clientCode: 'C003',
          clientName: '第三方集成API',
          clientType: 'API客户端',
          version: 'v3.0.1',
          detectFrequency: '每2小时',
          repeatMethod: '增量',
          dataSourceMethod: 'API接口',
          targetUploadMethod: 'WebService',
          runTime: '2024-01-17 09:15:00',
          apiUrl: 'https://integration.ddi.com/api',
          status: '启用',
          updateTime: '2024-01-17 09:15:00'
        },
        {
          id: 4,
          code: 'K0004',
          distributorName: '南京医药股份有限公司第一药店',
          clientCode: 'C004',
          clientName: '数据同步客户端',
          clientType: 'Windows客户端',
          version: 'v1.5.2',
          detectFrequency: '每6小时',
          repeatMethod: '覆盖',
          dataSourceMethod: '文件监控',
          targetUploadMethod: 'REST API',
          runTime: '2024-01-18 16:45:00',
          apiUrl: 'https://sync.ddi.com/api/v1',
          status: '停用',
          updateTime: '2024-01-18 16:45:00'
        },
        {
          id: 5,
          code: 'K0005',
          distributorName: '天津同仁堂经销商',
          clientCode: 'C005',
          clientName: '监控客户端',
          clientType: 'Linux客户端',
          version: 'v2.3.0',
          detectFrequency: '每12小时',
          repeatMethod: '增量',
          dataSourceMethod: '数据库同步',
          targetUploadMethod: 'MQ消息',
          runTime: '2024-01-19 11:20:00',
          apiUrl: 'https://monitor.ddi.com/api',
          status: '启用',
          updateTime: '2024-01-19 11:20:00'
        }
      ]
    }
  },
  mounted() {
    this.loadClientConfigList();
  },
  methods: {
    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadClientConfigList();
    },

    // 加载客户端配置列表数据
    loadClientConfigList() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.clientConfigList;

        // 根据客户端编码过滤
        if (this.filter.clientCode) {
          filteredList = filteredList.filter(item =>
            item.clientCode.includes(this.filter.clientCode)
          );
        }

        // 根据客户端名称过滤
        if (this.filter.clientName) {
          filteredList = filteredList.filter(item =>
            item.clientName.includes(this.filter.clientName)
          );
        }

        // 根据客户端类型过滤
        if (this.filter.clientType) {
          filteredList = filteredList.filter(item =>
            item.clientType === this.filter.clientType
          );
        }

        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.clientConfigList = filteredList.slice(start, end);

        this.loading = false;
      }, 500);
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadClientConfigList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.page = page;
      this.loadClientConfigList();
    },
    // 新增配置
    addClientConfig() {
      this.$message.info('新增配置功能开发中...');
    },
    // 编辑配置
    editClientConfig(row) {
      this.$message.info(`编辑配置：${row.clientName}`);
    },
    // 删除配置
    deleteClientConfig(row) {
      this.$confirm(`确定要删除客户端"${row.clientName}"的配置吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    }
  }
}
</script>
