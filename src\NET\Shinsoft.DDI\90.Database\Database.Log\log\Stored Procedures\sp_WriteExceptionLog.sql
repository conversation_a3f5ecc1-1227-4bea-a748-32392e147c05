﻿CREATE PROCEDURE [log].[sp_WriteExceptionLog]
	@ID							UNIQUEIDENTIFIER,
	@CompanyId					NVARCHAR(50),
	@CompanyCode				NVARCHAR(50),
	@CompanyName				NVARCHAR(200),
	@Category					NVARCHAR(50),
	@Level						NVARCHAR(50),
	@LogTime					DATETIME,
	@Logger						NVARCHAR(500),
	@Platform					NVARCHAR(200),
	@Program					NVARCHAR(200),
	@Operate					NVARCHAR(200),
	@Job						NVARCHAR(200),
	@Message					NVARCHAR(MAX),
	@Duration					BIGINT,
	@Remark						NVARCHAR(MAX),
	@Culture					NVARCHAR(10),
	@UserId						NVARCHAR(50),
	@UserUniqueName				NVARCHAR(50),
	@UserDisplayName			NVARCHAR(50),
	@EmployeeId					NVARCHAR(50),
	@EmployeeName		        NVARCHAR(50),
	@AgentId					NVARCHAR(50),
	@AgentName			        NVARCHAR(50),
	@Controller					NVARCHAR(500),
	@Action						NVARCHAR(500),
	@Method						NVARCHAR(50),
	@Headers					NVARCHAR(MAX),
	@Url						NVARCHAR(MAX),
	@IsAuthenticated			BIT,
	@QueryString				NVARCHAR(MAX),
	@UserAgent					NVARCHAR(500),
	@Identity					NVARCHAR(500),
	@Host						NVARCHAR(200),
	@IP							NVARCHAR(50),
	@TargetName					NVARCHAR(200),
	@TargetType					NVARCHAR(200),
	@TargetId					NVARCHAR(50),
	@ApiType					NVARCHAR(20),
	@Succeed					BIT,
	@Input						NVARCHAR(MAX),
	@OutHeaders					NVARCHAR(MAX),
	@Output						NVARCHAR(MAX),
	@InterfaceSite				NVARCHAR(200),
	@InterfaceName				NVARCHAR(200),
	@InterfaceAddress			NVARCHAR(500),
	@InterfaceMethod			NVARCHAR(50),
	@InterfaceHeader			NVARCHAR(2000),
	@InterfaceRequest			NVARCHAR(MAX),
	@InterfaceResponse			NVARCHAR(MAX),
	@Exception					NVARCHAR(MAX),
	@StackTrace					NVARCHAR(MAX)
AS
BEGIN
	SET NOCOUNT ON;

	EXEC log.sp_WriteLog 
		@ID,						-- uniqueidentifier
		@CompanyId,
		@CompanyCode,
		@CompanyName,
        @Category,                  -- nvarchar(50)
        @Level,                     -- nvarchar(50)
        @LogTime,					-- datetime
        @Logger,                    -- nvarchar(500)
        @Platform,                  -- nvarchar(200)
        @Program,                   -- nvarchar(200)
        @Operate,                   -- nvarchar(200)
        @Job,						-- nvarchar(200)
        @Message,                   -- nvarchar(max)
        @Duration	,				-- bigint
        @Remark,                    -- nvarchar(max)
        @Culture,                   -- nvarchar(10)
        @UserId,                    -- nvarchar(50)
        @UserUniqueName,            -- nvarchar(50)
        @UserDisplayName,           -- nvarchar(50)
        @EmployeeId,                -- nvarchar(50)
        @EmployeeName,              -- nvarchar(50)
        @AgentId,					-- nvarchar(50)
        @AgentName,			        -- nvarchar(50)
        @Controller,                -- nvarchar(500)
        @Action,                    -- nvarchar(500)
        @Method,                    -- nvarchar(50)
        @Headers,                   -- nvarchar(max)
        @Url,                       -- nvarchar(max)
        @IsAuthenticated,			-- bit
        @QueryString,               -- nvarchar(max)
        @UserAgent,                 -- nvarchar(200)
        @Identity,                  -- nvarchar(50)
        @Host,                      -- nvarchar(200)
        @IP,                        -- nvarchar(50)
        @TargetName,                -- nvarchar(200)
        @TargetType,                -- nvarchar(200)
        @TargetId,                  -- nvarchar(50)
        @ApiType,					-- int
		@Succeed,					-- bit
        @Input,                     -- nvarchar(max)
		@OutHeaders,                -- nvarchar(max)
        @Output,                    -- nvarchar(max)
		@InterfaceSite,
		@InterfaceName,
		@InterfaceAddress,
		@InterfaceMethod,
		@InterfaceHeader,
		@InterfaceRequest,
		@InterfaceResponse
	;

	SET @Exception = RTRIM(LTRIM(ISNULL(@Exception,'')));
	SET @StackTrace = RTRIM(LTRIM(ISNULL(@StackTrace,'')));

	INSERT INTO [log].[LogException] (
		[ID],
		[CompanyId],
		[Exception],
		[StackTrace]
	) VALUES (
		@ID,			-- ID - uniqueidentifier
		@CompanyId,
		@Exception,		-- Exception - nvarchar(max)
		@StackTrace		-- StackTrace - nvarchar(max)
    )


END
GO

GRANT EXEC ON [log].[sp_WriteExceptionLog] TO PUBLIC
GO
