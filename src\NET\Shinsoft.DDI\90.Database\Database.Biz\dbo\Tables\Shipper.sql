CREATE TABLE [dbo].[Shipper] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [Default_Shipper_ID] DEFAULT (NEWSEQUENTIALID()),    
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(200)               NOT NULL,
    [ShortName]                 NVARCHAR(50)                NOT NULL,
    [Address]                   NVARCHAR(500)               NULL,
    Telephone                   NVARCHAR(50)                NULL,
    EMail                       NVARCHAR(50)                NULL,
    ContactPerson               NVARCHAR(50)                NULL,
    [EnumStatus]                INT                         NOT NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL,
    [CreateTime]                DATETIME                    NULL,
    [LastEditor]                NVARCHAR(50)                NULL,
    [LastEditTime]              DATETIME                    NULL,
    CONSTRAINT [PK_Shipper] PRIMARY KEY CLUSTERED ([ID])    
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'发货方',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
--EXEC sp_addextendedproperty @name = N'MS_Description',
--    @value = N'公司ID',
--    @level0type = N'SCHEMA',
--    @level0name = N'dbo',
--    @level1type = N'TABLE',
--    @level1name = N'Shipper',
--    @level2type = N'COLUMN',
--    @level2name = N'CompanyId'
--GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'发货方编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'发货方名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'发货方简称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'ShortName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'地址',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'Address'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'EnumStatus'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Shipper',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
