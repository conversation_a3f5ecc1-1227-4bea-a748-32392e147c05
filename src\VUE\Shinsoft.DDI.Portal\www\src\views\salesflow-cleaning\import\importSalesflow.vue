<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>流向清洗</el-breadcrumb-item>
        <el-breadcrumb-item>流向导入日志</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="8" type="flex">
        <el-col :span="4">
          <el-input v-model="filter.fileName" placeholder="原始文件名称" clearable />
        </el-col>
        <el-col :span="4">
          <el-input v-model="filter.distributorName" placeholder="经销商名称" clearable />
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="filter.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            popper-class="date-picker-dropdown"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="Upload" @click="importData">导入</el-button>
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="importSalesFlowList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="fileName" label="原始文件名称" min-width="200" />
        <el-table-column prop="distributorName" label="经销商名称" min-width="150" />
        <el-table-column prop="distributorCode" label="经销商Code" width="120" />
        <el-table-column prop="importMethod" label="导入方式" width="100" />
        <el-table-column prop="importTime" label="导入时间" width="160" />
        <el-table-column prop="totalCount" label="导入总数" width="100" />
        <el-table-column prop="errorCount" label="错误数" width="80" />
        <el-table-column prop="processedCount" label="已处理" width="80" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="错误处理" placement="top">
              <el-button
                icon="Tools"
                circle
                size="small"
                @click="openErrorDialog(row)"
                :disabled="row.errorCount === 0"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="search"
        @current-change="search"
      />
    </div>

    <!-- 错误处理对话框 -->
    <el-dialog
      v-model="showErrorDialog"
      title="错误处理"
      width="1200px"
      :close-on-click-modal="false"
      @close="handleCloseErrorDialog"
    >
      <!-- 查询条件区域 -->
      <div class="search-container">
        <el-row :gutter="16" type="flex">
          <el-col :span="6">
            <el-input
              v-model="errorSearchFilter.errorDesc"
              placeholder="错误描述"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="errorSearchFilter.isProcessed"
              placeholder="处理状态"
              clearable
              style="width: 100%"
            >
              <el-option label="全部" value="" />
              <el-option label="已处理" :value="true" />
              <el-option label="未处理" :value="false" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="searchErrorData">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 对话框操作按钮区域 -->
      <div class="dialog-action-container">
        <div class="action-buttons">
          <el-button icon="Download" @click="exportErrorData">导出错误数据</el-button>
        </div>
      </div>

      <!-- 错误数据表格 -->
      <div class="error-table-container">
        <el-table :data="errorDataList" stripe size="small" v-loading="errorLoading" max-height="400">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="shipperProvince" label="发货方省份" width="100" />
          <el-table-column prop="shipperCity" label="发货方城市" width="100" />
          <el-table-column prop="shipperName" label="发货方名称" min-width="150" />
          <el-table-column prop="receiverProvince" label="收货方省份" width="100" />
          <el-table-column prop="receiverCity" label="收货方城市" width="100" />
          <el-table-column prop="receiverName" label="收货方名称" min-width="150" />
          <el-table-column prop="manufacturerName" label="厂商名称" min-width="120" />
          <el-table-column prop="productName" label="产品名称" min-width="150" />
          <el-table-column prop="genericName" label="通用名" min-width="120" />
          <el-table-column prop="specification" label="规格" width="100" />
          <el-table-column prop="batchNumber" label="批号" width="100" />
          <el-table-column prop="saleDate" label="销售日期" width="100" />
          <el-table-column prop="saleQuantity" label="销售数量" width="100" />
          <el-table-column prop="errorDesc" label="错误描述" min-width="200" fixed="right" />
          <el-table-column prop="isProcessed" label="是否处理" width="80" fixed="right">
            <template #default="{ row }">
              <el-tag
                :type="row.isProcessed ? 'success' : 'warning'"
                size="small"
              >
                {{ row.isProcessed ? '已处理' : '未处理' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-tooltip content="处理" placement="top">
                <el-button
                  icon="Check"
                  circle
                  size="small"
                  @click="processError(row)"
                  :disabled="row.isProcessed"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 错误数据分页 -->
      <div class="error-pagination-container">
        <el-pagination
          v-model:current-page="errorFilter.page"
          v-model:page-size="errorFilter.per"
          :page-sizes="pageSizeOpts"
          :total="errorTotalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="searchErrorData"
          @current-change="searchErrorData"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ImportLog',
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        fileName: '',
        distributorName: '',
        dateRange: []
      },
      totalCount: 0,
      // 导入销售流向数据列表
      importSalesFlowList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          fileName: '北京医药_2024年1月销售数据.xlsx',
          distributorName: '北京医药经销商',
          distributorCode: 'BJ001',
          importMethod: 'DDI导入',
          importTime: '2024-01-20 09:30:00',
          totalCount: 1500,
          errorCount: 25,
          processedCount: 10
        },
        {
          id: 2,
          fileName: '上海康健_销售流向_202401.xlsx',
          distributorName: '上海康健医药',
          distributorCode: 'SH002',
          importMethod: '手工导入',
          importTime: '2024-01-20 10:15:00',
          totalCount: 2300,
          errorCount: 18,
          processedCount: 18
        },
        {
          id: 3,
          fileName: '广州南方医药销售数据_20240120.xlsx',
          distributorName: '广州南方经销商',
          distributorCode: 'GZ003',
          importMethod: 'DDI导入',
          importTime: '2024-01-20 11:20:00',
          totalCount: 890,
          errorCount: 5,
          processedCount: 2
        },
        {
          id: 4,
          fileName: '深圳华润_流向数据_2024Q1.xlsx',
          distributorName: '深圳华润经销商',
          distributorCode: 'SZ004',
          importMethod: '手工导入',
          importTime: '2024-01-20 14:45:00',
          totalCount: 3200,
          errorCount: 0,
          processedCount: 0
        },
        {
          id: 5,
          fileName: '天津同仁堂销售明细_202401.xlsx',
          distributorName: '天津同仁堂经销商',
          distributorCode: 'TJ005',
          importMethod: 'DDI导入',
          importTime: '2024-01-20 16:30:00',
          totalCount: 1200,
          errorCount: 12,
          processedCount: 8
        }
      ],
      // 错误处理对话框相关数据
      showErrorDialog: false,
      errorLoading: false,
      currentImportRecord: null,
      errorFilter: {
        page: 1,
        per: 10
      },
      // 错误数据搜索过滤器
      errorSearchFilter: {
        errorDesc: '',
        isProcessed: ''
      },
      errorTotalCount: 0,
      errorDataList: []
    }
  },
  mounted() {
    this.search();
  },
  methods: {
    // 查询方法
    search() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.importSalesFlowList;

        // 根据文件名过滤
        if (this.filter.fileName) {
          filteredList = filteredList.filter(item =>
            item.fileName.includes(this.filter.fileName)
          );
        }

        // 根据经销商名称过滤
        if (this.filter.distributorName) {
          filteredList = filteredList.filter(item =>
            item.distributorName.includes(this.filter.distributorName)
          );
        }

        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.importSalesFlowList = filteredList.slice(start, end);

        this.loading = false;
      }, 500);
    },

    // 打开错误处理对话框
    openErrorDialog(row) {
      this.currentImportRecord = row;
      this.showErrorDialog = true;
      this.loadErrorData();
    },

    // 加载错误数据
    loadErrorData() {
      this.errorLoading = true;

      // 模拟错误数据，实际使用时需要从API获取
      setTimeout(() => {
        let allErrorData = [
          {
            id: 1,
            shipperProvince: '北京市',
            shipperCity: '北京市',
            shipperName: '北京制药厂',
            receiverProvince: '上海市',
            receiverCity: '上海市',
            receiverName: '上海医药公司',
            manufacturerName: '拜耳制药',
            productName: '阿司匹林肠溶片',
            genericName: '阿司匹林',
            specification: '100mg*30片',
            batchNumber: '*********',
            saleDate: '2024-01-15',
            saleQuantity: '100盒',
            errorDesc: '产品规格信息不匹配',
            isProcessed: false
          },
          {
            id: 2,
            shipperProvince: '江苏省',
            shipperCity: '南京市',
            shipperName: '江苏医药集团',
            receiverProvince: '浙江省',
            receiverCity: '杭州市',
            receiverName: '杭州康复医院',
            manufacturerName: '华润制药',
            productName: '青霉素注射液',
            genericName: '青霉素',
            specification: '80万单位',
            batchNumber: 'H20240118',
            saleDate: '2024-01-18',
            saleQuantity: '50支',
            errorDesc: '收货方名称无法匹配',
            isProcessed: true
          }
        ];

        // 应用搜索过滤条件
        let filteredData = allErrorData;

        // 按错误描述过滤
        if (this.errorSearchFilter.errorDesc) {
          filteredData = filteredData.filter(item =>
            item.errorDesc.includes(this.errorSearchFilter.errorDesc)
          );
        }

        // 按处理状态过滤
        if (this.errorSearchFilter.isProcessed !== '') {
          filteredData = filteredData.filter(item =>
            item.isProcessed === this.errorSearchFilter.isProcessed
          );
        }

        this.errorDataList = filteredData;
        this.errorTotalCount = filteredData.length;
        this.errorLoading = false;
      }, 300);
    },

    // 搜索错误数据
    searchErrorData() {
      // 重置分页到第一页
      this.errorFilter.page = 1;
      this.loadErrorData();
    },

    // 处理单个错误
    processError(row) {
      this.$message.success(`已处理错误：${row.errorDesc}`);
      row.isProcessed = true;
    },

    // 关闭错误处理对话框
    handleCloseErrorDialog() {
      this.showErrorDialog = false;
      this.currentImportRecord = null;
      this.errorDataList = [];
      // 重置搜索条件
      this.errorSearchFilter.errorDesc = '';
      this.errorSearchFilter.isProcessed = '';
      this.errorFilter.page = 1;
    },

    // 导出错误数据
    exportErrorData() {
      this.$message.info('导出错误数据功能开发中...');
    },

    // 导入数据
    importData() {
      this.$message.info('导入功能开发中...');
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    }
  }
}
</script>