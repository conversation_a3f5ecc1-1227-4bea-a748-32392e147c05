<!--
/**
 * 厂家管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑厂家' : '新增厂家'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="manufacturerFormRef"
      :model="manufacturerForm"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <!-- 厂家编码 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="厂家编码" prop="Code">
            <el-input v-model="manufacturerForm.Code" placeholder="请输入厂家编码" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="isEdit ? 12 : 24">
          <el-form-item label="厂家名称" prop="Name">
            <el-input v-model="manufacturerForm.Name" placeholder="请输入厂家名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="厂家简称" prop="ShortName">
            <el-input v-model="manufacturerForm.ShortName" placeholder="请输入厂家简称" />
          </el-form-item>
        </el-col>
        <!-- 状态 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="状态" prop="EnumStatus">
            <el-select v-model="manufacturerForm.EnumStatus" placeholder="请选择状态" style="width: 100%" :disabled="true">
              <el-option label="启用" :value="0" />
              <el-option label="停用" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="国家" prop="Country">
            <el-input v-model="manufacturerForm.Country" placeholder="请输入国家" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="备注" prop="Remark">
            <el-input 
              v-model="manufacturerForm.Remark" 
              type="textarea" 
              :rows="3"
              placeholder="请输入备注" 
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'manufacturerDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const manufacturerFormRef = ref(null)
    
    // 保存加载状态
    const saveLoading = ref(false)
    
    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })
    
    // 表单数据
    const manufacturerForm = reactive({
      Code: '',
      Name: '',
      ShortName: '',
      Country: '',
      EnumStatus: null,
      Remark: ''
    })

    // 表单验证规则 - 动态规则，根据编辑模式调整
    const formRules = computed(() => ({
      Code: isEdit.value ? [
        { required: true, message: '请输入厂家编码', trigger: 'blur' },
        { max: 50, message: '编码长度不能超过50个字符', trigger: 'blur' }
      ] : [],
      Name: [
        { required: true, message: '请输入厂家名称', trigger: 'blur' },
        { max: 300, message: '名称长度不能超过300个字符', trigger: 'blur' }
      ],
      ShortName: [
        { max: 50, message: '简称长度不能超过50个字符', trigger: 'blur' }
      ],
      Country: [
        { max: 50, message: '国家长度不能超过50个字符', trigger: 'blur' }
      ],
      EnumStatus: isEdit.value ? [
        { required: true, message: '请选择状态', trigger: 'change' }
      ] : [],
      Remark: [
        { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
      ]
    }))

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(manufacturerForm, {
        Code: '',
        Name: '',
        ShortName: '',
        Country: '',
        EnumStatus: isEdit.value ? 0 : null, // 新增时不设置默认状态
        Remark: ''
      })
      
      if (manufacturerFormRef.value) {
        manufacturerFormRef.value.resetFields()
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = (recordId) => {
      // TODO: 实际项目中需要调用API接口获取数据
      // this.$http.get(`/Manufacturer/GetManufacturer/${recordId}`).then(response => {
      //   if (response.success) {
      //     Object.assign(manufacturerForm, response.data);
      //   } else {
      //     ElMessage.error(response.message || '加载数据失败');
      //   }
      // });

      // 模拟数据加载
      console.log('加载厂家数据，ID:', recordId)
      setTimeout(() => {
        const mockData = {
          Code: 'M001',
          Name: '拜耳医药保健有限公司',
          ShortName: '拜耳医药',
          Country: '德国',
          EnumStatus: 0,
          Remark: '全球知名制药企业'
        }
        Object.assign(manufacturerForm, mockData)
      }, 100)
    }

    /**
     * 保存厂家信息
     */
    const handleSave = () => {
      manufacturerFormRef.value.validate((valid) => {
        if (valid) {
          saveLoading.value = true

          // TODO: 实际项目中需要调用API接口
          // const apiUrl = isEdit.value ? '/Manufacturer/UpdateManufacturer' : '/Manufacturer/CreateManufacturer';
          // this.$http.post(apiUrl, manufacturerForm).then(response => {
          //   if (response.success) {
          //     ElMessage.success(isEdit.value ? '厂家信息更新成功' : '厂家添加成功');
          //     emit('success');
          //     handleClose();
          //   } else {
          //     ElMessage.error(response.message || '操作失败');
          //   }
          //   saveLoading.value = false;
          // }).catch(error => {
          //   ElMessage.error('操作失败：' + error.message);
          //   saveLoading.value = false;
          // });

          // 模拟保存操作
          setTimeout(() => {
            ElMessage.success(isEdit.value ? '厂家信息更新成功' : '厂家添加成功')
            saveLoading.value = false
            emit('success')
            handleClose()
          }, 1000)
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      }
    })

    return {
      manufacturerFormRef,
      saveLoading,
      isEdit,
      dialogVisible,
      manufacturerForm,
      formRules,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>
