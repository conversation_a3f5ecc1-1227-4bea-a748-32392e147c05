﻿CREATE TABLE [dbo].[Receiver]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Receiver_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [SdrCode]                   NVARCHAR(50)                NOT NULL,   --SDR 授权码
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(500)               NOT NULL,
    [EnumStatus]                INT                         NOT NULL,
    [Remark]                    NVARCHAR(500)               NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_Receiver] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Receiver_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),

)
GO

CREATE INDEX [IX_Receiver_SdrCode] ON [dbo].[Receiver]
(
	[SdrCode] ASC
);

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'经销商/收货方',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'授权码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'SdrCode'