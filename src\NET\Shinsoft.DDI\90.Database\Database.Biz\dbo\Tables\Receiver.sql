﻿CREATE TABLE [dbo].[Receiver]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Receiver_ID]  DEFAULT (NEWSEQUENTIALID()),
    [SdrCode]                   NVARCHAR(50)                NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(500)               NOT NULL,    
    [ProvinceId]                UNIQUEIDENTIFIER            NOT NULL,
    [CityId]                    UNIQUEIDENTIFIER            NULL,
    [CountyId]                  UNIQUEIDENTIFIER            NULL,
    [ReceiverTypeId]            UNIQUEIDENTIFIER            NOT NULL,
    UnifiedSocialCreditCode     NVARCHAR(50)               NULL,
    [Address]                   NVARCHAR(500)               NULL,
    Telephone                   NVARCHAR(50)               NULL,
    EMail                       NVARCHAR(50)               NULL,
    PostalCode                  NVARCHAR(50)               NULL,
    NetAddress                  NVARCHAR(50)               NULL,
    StopTime                    DateTime                    NULL,
    TargetTerminalId            UNIQUEIDENTIFIER            NULL,
    Hospital<PERSON>radeId             UNIQUEIDENTIFIER            NULL,
    HospitalLevelId             UNIQUEIDENTIFIER            NULL,
    [EnumStatus]                INT                         NOT NULL,
    [Remark]                    NVARCHAR(500)               NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL,
    [CreateTime]			    DATETIME			        NULL,
    [LastEditor]			    NVARCHAR(50)		        NULL,
    [LastEditTime]			    DATETIME			        NULL,
    CONSTRAINT [PK_Receiver] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Receiver_Province] FOREIGN KEY ([ProvinceId]) REFERENCES [dbo].[Province] ([ID]),
    CONSTRAINT [FK_Receiver_City] FOREIGN KEY ([CityId]) REFERENCES [dbo].[City] ([ID]),
    CONSTRAINT [FK_Receiver_County] FOREIGN KEY ([CountyId]) REFERENCES [dbo].[County] ([ID]),
    CONSTRAINT [FK_Receiver_ReceiverType] FOREIGN KEY ([ReceiverTypeId]) REFERENCES [dbo].[ReceiverType] ([ID]),
    CONSTRAINT [FK_Receiver_Receiver_00_TargetTermina_DiscontinuedTerminal] FOREIGN KEY (TargetTerminalId) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_Receiver_Dict_00_HospitalGrade] FOREIGN KEY (HospitalGradeId) REFERENCES [dbo].Dict ([ID]),
    CONSTRAINT [FK_Receiver_Dict_01_HospitalLeve] FOREIGN KEY (HospitalLevelId) REFERENCES [dbo].Dict ([ID])
)
GO

CREATE INDEX [IX_Receiver_SdrCode] ON [dbo].[Receiver]
(
	[SdrCode] ASC
);

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'经销商/收货方',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'授权码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'SdrCode'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'地址',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Address'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'省份ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'ProvinceId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'城市ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'CityId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'区县ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'CountyId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方类型ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverTypeId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'EnumStatus'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Remark'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Receiver',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'