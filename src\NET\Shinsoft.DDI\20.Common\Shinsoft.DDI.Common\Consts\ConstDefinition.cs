using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public static class ConstDefinition
    {
        public class System
        {
            public const string CreateTimeColumn = "CreateTime";
            public const string LastEditTimeColumn = "LastEditTime";
            public const string NoticeTemplate_NewLine_Old = "\\n";
            public const string NoticeTemplate_NewLine_New = "\n";
            public const int BulkInsertTimeOut = 600;
            public const int TransactionScopeTimeoutMinute = 20;
            /// <summary>
            /// 存储过程超时时间 10分钟（单位：秒）
            /// </summary>
            public const int CommandTimeout = 600;
        }

        public class Common
        {      
            public const string Export_ContentType = "application/octet-stream";
            public const string ExtensionOfExcel = ".xls";
            public const string ExtensionOfHighVersionExcel = ".xlsx";
            public const string ExtensionOfHighVersionWord = ".docx";
        }

        public class CodePrefix {
            public const string Shipper_CodePrefix = "SH";
            public const string Receiver_CodePrefix = "RE";
            public const string Product_CodePrefix = "PR";
        }
    }
}