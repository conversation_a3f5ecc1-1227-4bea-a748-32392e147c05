<!--产品管理页面 - 基于Element Plus组件-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>主数据管理</el-breadcrumb-item>
        <el-breadcrumb-item>产品管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-cascader
            v-model="selectedProduct"
            :options="productCascaderOptions"
            placeholder="产品/分型/规格"
            clearable
            :props="cascaderProps"
            @change="handleProductCascaderChange"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.genericName"
            placeholder="通用名"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="OfficeBuilding" @click="showManufacturerManagementDialog">厂家管理</el-button>
        <el-button icon="Setting" @click="showProductManagementDialog">产品管理</el-button>
        <el-button icon="CirclePlus" @click="addSpec">新增规格</el-button>
        <el-button icon="Download" @click="exportProductList">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="productList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="productCode" label="产品编码" width="120" />
        <el-table-column prop="productName" label="产品名称" min-width="180" />
        <el-table-column prop="genericName" label="通用名" width="150" />
        <el-table-column prop="englishName" label="英文名称" width="150" />
        <el-table-column prop="dosageForm" label="剂型" width="100" />
        <el-table-column prop="manufacturer" label="生产厂家" width="150" />
        <el-table-column prop="packageSpec" label="包装规格" width="120" />
        <el-table-column prop="categoryName" label="分型名称" width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editProduct(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" @click="removeProduct(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 产品管理对话框 -->
    <el-dialog
      v-model="showProductDialog"
      title="产品管理"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleCloseProductDialog"
    >
      <!-- 查询条件区域 -->
      <div class="dialog-search-container">
        <el-row :gutter="16" type="flex">
          <el-col :span="6">
            <el-input
              v-model="productDialogFilter.productName"
              placeholder="产品名称"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="searchProductDialog">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 对话框操作按钮区域 -->
      <div class="dialog-action-container">
        <div class="action-buttons">
          <el-button icon="CirclePlus" @click="addProductInDialog">新增产品</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table :data="productDialogList" stripe size="small" v-loading="productDialogLoading">
          <el-table-column prop="productName" label="产品名称" min-width="200" />
          <el-table-column prop="genericName" label="通用名" width="180" />
          <el-table-column prop="englishName" label="英文名称" width="200" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
                <el-tooltip content="编辑" placement="top">
                  <el-button icon="Edit" circle size="small" @click="editProductInDialog(row)" />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button icon="Delete" circle size="small" @click="removeProductInDialog(row)" />
                </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="productDialogFilter.page"
          v-model:page-size="productDialogFilter.per"
          :page-sizes="[10, 20, 50, 100]"
          :total="productDialogTotalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="changeProductDialogPageSize"
          @current-change="changeProductDialogPage"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseProductDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 厂家管理对话框 -->
    <el-dialog
      v-model="showManufacturerDialog"
      title="厂家管理"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleCloseManufacturerDialog"
    >
      <!-- 查询条件区域 -->
      <div class="dialog-search-container">
        <el-row :gutter="16" type="flex">
          <el-col :span="6">
            <el-input
              v-model="manufacturerDialogFilter.manufacturerName"
              placeholder="厂家名称"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="searchManufacturerDialog">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 对话框操作按钮区域 -->
      <div class="dialog-action-container">
        <div class="action-buttons">
          <el-button icon="CirclePlus" @click="addManufacturerInDialog">新增厂家</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table :data="manufacturerDialogList" stripe size="small" v-loading="manufacturerDialogLoading">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="manufacturerCode" label="厂家编号" width="150" />
          <el-table-column prop="manufacturerName" label="厂家名称" min-width="200" />
          <el-table-column prop="nationality" label="国籍" width="120" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
                <el-tooltip content="编辑" placement="top">
                  <el-button icon="Edit" circle size="small" @click="editManufacturerInDialog(row)" />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button icon="Delete" circle size="small" @click="removeManufacturerInDialog(row)" />
                </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="manufacturerDialogFilter.page"
          v-model:page-size="manufacturerDialogFilter.per"
          :page-sizes="[10, 20, 50, 100]"
          :total="manufacturerDialogTotalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="changeManufacturerDialogPageSize"
          @current-change="changeManufacturerDialogPage"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseManufacturerDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ProductList',
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      selectedProduct: [],
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        checkStrictly: true // 允许选择任意级别
      },
      productCascaderOptions: [
        {
          value: 'bayer',
          label: '拜耳医药',
          children: [
            {
              value: 'aspirin',
              label: '阿司匹林肠溶片',
              children: [
                { value: '10mg', label: '10mg' },
                { value: '20mg', label: '20mg' },
                { value: '100mg', label: '100mg' }
              ]
            },
            {
              value: 'ibuprofen',
              label: '布洛芬缓释胶囊',
              children: [
                { value: '200mg', label: '200mg' },
                { value: '300mg', label: '300mg' }
              ]
            }
          ]
        },
        {
          value: 'huabei',
          label: '华北制药',
          children: [
            {
              value: 'penicillin',
              label: '青霉素注射液',
              children: [
                { value: '5ml', label: '5ml' },
                { value: '10ml', label: '10ml' }
              ]
            }
          ]
        },
        {
          value: 'yangzijiang',
          label: '扬子江药业',
          children: [
            {
              value: 'ibuprofen_capsule',
              label: '布洛芬缓释胶囊',
              children: [
                { value: '300mg', label: '300mg' },
                { value: '400mg', label: '400mg' }
              ]
            }
          ]
        },
        {
          value: 'medtronic',
          label: '美敦力',
          children: [
            {
              value: 'surgical_blade',
              label: '手术刀片',
              children: [
                { value: 'type_a', label: 'A型' },
                { value: 'type_b', label: 'B型' }
              ]
            }
          ]
        },
        {
          value: 'johnson',
          label: '强生医疗',
          children: [
            {
              value: 'forceps',
              label: '医用镊子',
              children: [
                { value: 'standard', label: '标准型' },
                { value: 'precision', label: '精密型' }
              ]
            }
          ]
        }
      ],
      filter: {
        page: 1,
        per: 10,
        genericName: '',
        productCategory: '',
        productType: '',
        productSpec: ''
      },
      totalCount: 0,
      productList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          productCode: 'P001',
          productName: '阿司匹林肠溶片',
          genericName: '阿司匹林',
          englishName: 'Aspirin Enteric-coated Tablets',
          categoryName: '进口',
          dosageForm: '片剂',
          manufacturer: '拜耳医药',
          packageSpec: '100mg*30片',
          category: 'bayer',
          type: 'aspirin',
          spec: '10mg'
        },
        {
          id: 2,
          productCode: 'P002',
          productName: '青霉素注射液',
          genericName: '青霉素',
          englishName: 'Penicillin Injection',
          categoryName: '国产',
          dosageForm: '注射剂',
          manufacturer: '华北制药',
          packageSpec: '80万单位*10支',
          category: 'huabei',
          type: 'penicillin',
          spec: '5ml'
        },
        {
          id: 3,
          productCode: 'P003',
          productName: '布洛芬缓释胶囊',
          genericName: '布洛芬',
          englishName: 'Ibuprofen Sustained-release Capsules',
          categoryName: '国产分包',
          dosageForm: '胶囊剂',
          manufacturer: '扬子江药业',
          packageSpec: '300mg*20粒',
          category: 'yangzijiang',
          type: 'ibuprofen_capsule',
          spec: '300mg'
        },
        {
          id: 4,
          productCode: 'P004',
          productName: '手术刀片',
          genericName: '一次性手术刀',
          englishName: 'Disposable Surgical Blade',
          categoryName: '进口',
          dosageForm: '器械',
          manufacturer: '美敦力',
          packageSpec: '10片/盒',
          category: 'medtronic',
          type: 'surgical_blade',
          spec: 'type_a'
        },
        {
          id: 5,
          productCode: 'P005',
          productName: '医用镊子',
          genericName: '无菌镊子',
          englishName: 'Sterile Forceps',
          categoryName: '国产压片',
          dosageForm: '器械',
          manufacturer: '强生医疗',
          packageSpec: '1支/包',
          category: 'johnson',
          type: 'forceps',
          spec: 'standard'
        }
      ],
      // 产品管理对话框相关数据
      showProductDialog: false,
      productDialogLoading: false,
      productDialogFilter: {
        page: 1,
        per: 10,
        productName: ''
      },
      productDialogTotalCount: 0,
      productDialogList: [
        // 模拟产品管理对话框数据
        {
          id: 1,
          productName: '阿司匹林肠溶片',
          genericName: '阿司匹林',
          englishName: 'Aspirin Enteric-coated Tablets'
        },
        {
          id: 2,
          productName: '青霉素注射液',
          genericName: '青霉素',
          englishName: 'Penicillin Injection'
        },
        {
          id: 3,
          productName: '布洛芬缓释胶囊',
          genericName: '布洛芬',
          englishName: 'Ibuprofen Sustained-release Capsules'
        },
        {
          id: 4,
          productName: '头孢克肟颗粒',
          genericName: '头孢克肟',
          englishName: 'Cefixime Granules'
        },
        {
          id: 5,
          productName: '甲硝唑片',
          genericName: '甲硝唑',
          englishName: 'Metronidazole Tablets'
        }
      ],
      // 厂家管理对话框相关数据
      showManufacturerDialog: false,
      manufacturerDialogLoading: false,
      manufacturerDialogFilter: {
        page: 1,
        per: 10,
        manufacturerName: ''
      },
      manufacturerDialogTotalCount: 0,
      manufacturerDialogList: [
        // 模拟厂家管理对话框数据
        {
          id: 1,
          manufacturerCode: 'M001',
          manufacturerName: '拜耳医药',
          nationality: '德国'
        },
        {
          id: 2,
          manufacturerCode: 'M002',
          manufacturerName: '华北制药',
          nationality: '中国'
        },
        {
          id: 3,
          manufacturerCode: 'M003',
          manufacturerName: '扬子江药业',
          nationality: '中国'
        },
        {
          id: 4,
          manufacturerCode: 'M004',
          manufacturerName: '美敦力',
          nationality: '美国'
        },
        {
          id: 5,
          manufacturerCode: 'M005',
          manufacturerName: '强生医疗',
          nationality: '美国'
        },
        {
          id: 6,
          manufacturerCode: 'M006',
          manufacturerName: '诺华制药',
          nationality: '瑞士'
        }
      ]
    };
  },
  mounted() {
    this.loadProductList();
  },
  methods: {
    /**
     * 加载产品列表数据
     */
    loadProductList() {
      this.loading = true;

      // TODO: 实际项目中需要调用API接口
      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.productList;

        // 根据通用名过滤
        if (this.filter.genericName) {
          filteredList = filteredList.filter(item =>
            item.genericName.includes(this.filter.genericName)
          );
        }

        // 根据级联选择器过滤
        if (this.filter.productCategory) {
          filteredList = filteredList.filter(item =>
            item.category === this.filter.productCategory
          );
        }

        if (this.filter.productType) {
          filteredList = filteredList.filter(item =>
            item.type === this.filter.productType
          );
        }

        if (this.filter.productSpec) {
          filteredList = filteredList.filter(item =>
            item.spec === this.filter.productSpec
          );
        }

        // 按产品名称排序
        filteredList.sort((a, b) => a.productName.localeCompare(b.productName));

        this.totalCount = filteredList.length;
        this.loading = false;
      }, 500);
    },

    /**
     * 产品级联选择器改变事件
     * @param {Array} value - 选中的值数组
     */
    handleProductCascaderChange(value) {
      if (value && value.length > 0) {
        this.filter.productCategory = value[0] || '';
        this.filter.productType = value[1] || '';
        this.filter.productSpec = value[2] || '';
      } else {
        this.filter.productCategory = '';
        this.filter.productType = '';
        this.filter.productSpec = '';
      }
    },

    /**
     * 查询按钮点击事件
     */
    search() {
      this.filter.page = 1;
      this.loadProductList();
    },

    /**
     * 分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadProductList();
    },

    /**
     * 页码改变事件
     * @param {number} page - 新的页码
     */
    changePage(page) {
      this.filter.page = page;
      this.loadProductList();
    },

    /**
     * 显示产品管理对话框
     */
    showProductManagementDialog() {
      this.showProductDialog = true;
      this.loadProductDialogList();
    },

    /**
     * 新增规格
     */
    addSpec() {
      console.log('新增规格');
      // TODO: 实现新增规格功能
    },

    /**
     * 编辑产品
     * @param {Object} row - 产品数据行
     */
    editProduct(row) {
      console.log('编辑产品', row);
      // TODO: 实现编辑产品功能
    },

    /**
     * 删除产品
     * @param {Object} row - 产品数据行
     */
    removeProduct(row) {
      console.log('删除产品', row);
      // TODO: 实现删除产品功能
    },

    /**
     * 导出产品列表
     */
    exportProductList() {
      console.log('导出产品列表');
      // TODO: 实现导出功能
    },

    /**
     * 加载产品管理对话框列表数据
     */
    loadProductDialogList() {
      this.productDialogLoading = true;

      // TODO: 实际项目中需要调用API接口
      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.productDialogList;

        if (this.productDialogFilter.productName) {
          filteredList = filteredList.filter(item =>
            item.productName.includes(this.productDialogFilter.productName)
          );
        }

        this.productDialogTotalCount = filteredList.length;
        this.productDialogLoading = false;
      }, 500);
    },

    /**
     * 产品管理对话框查询按钮点击事件
     */
    searchProductDialog() {
      this.productDialogFilter.page = 1;
      this.loadProductDialogList();
    },

    /**
     * 产品管理对话框分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changeProductDialogPageSize(size) {
      this.productDialogFilter.per = size;
      this.productDialogFilter.page = 1;
      this.loadProductDialogList();
    },

    /**
     * 产品管理对话框页码改变事件
     * @param {number} page - 新的页码
     */
    changeProductDialogPage(page) {
      this.productDialogFilter.page = page;
      this.loadProductDialogList();
    },

    /**
     * 关闭产品管理对话框
     */
    handleCloseProductDialog() {
      this.showProductDialog = false;
      // 重置查询条件
      this.productDialogFilter.productName = '';
      this.productDialogFilter.page = 1;
    },

    /**
     * 对话框中新增产品
     */
    addProductInDialog() {
      console.log('对话框中新增产品');
      // TODO: 实现对话框中新增产品功能
    },

    /**
     * 对话框中编辑产品
     * @param {Object} row - 产品数据行
     */
    editProductInDialog(row) {
      console.log('对话框中编辑产品', row);
      // TODO: 实现对话框中编辑产品功能
    },

    /**
     * 对话框中删除产品
     * @param {Object} row - 产品数据行
     */
    removeProductInDialog(row) {
      console.log('对话框中删除产品', row);
      // TODO: 实现对话框中删除产品功能
    },

    /**
     * 显示厂家管理对话框
     */
    showManufacturerManagementDialog() {
      this.showManufacturerDialog = true;
      this.loadManufacturerDialogList();
    },

    /**
     * 加载厂家管理对话框列表数据
     */
    loadManufacturerDialogList() {
      this.manufacturerDialogLoading = true;

      // TODO: 实际项目中需要调用API接口
      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.manufacturerDialogList;

        if (this.manufacturerDialogFilter.manufacturerName) {
          filteredList = filteredList.filter(item =>
            item.manufacturerName.includes(this.manufacturerDialogFilter.manufacturerName)
          );
        }

        this.manufacturerDialogTotalCount = filteredList.length;
        this.manufacturerDialogLoading = false;
      }, 500);
    },

    /**
     * 厂家管理对话框查询按钮点击事件
     */
    searchManufacturerDialog() {
      this.manufacturerDialogFilter.page = 1;
      this.loadManufacturerDialogList();
    },

    /**
     * 厂家管理对话框分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changeManufacturerDialogPageSize(size) {
      this.manufacturerDialogFilter.per = size;
      this.manufacturerDialogFilter.page = 1;
      this.loadManufacturerDialogList();
    },

    /**
     * 厂家管理对话框页码改变事件
     * @param {number} page - 新的页码
     */
    changeManufacturerDialogPage(page) {
      this.manufacturerDialogFilter.page = page;
      this.loadManufacturerDialogList();
    },

    /**
     * 关闭厂家管理对话框
     */
    handleCloseManufacturerDialog() {
      this.showManufacturerDialog = false;
      // 重置查询条件
      this.manufacturerDialogFilter.manufacturerName = '';
      this.manufacturerDialogFilter.page = 1;
    },

    /**
     * 对话框中新增厂家
     */
    addManufacturerInDialog() {
      console.log('对话框中新增厂家');
      // TODO: 实现对话框中新增厂家功能
    },

    /**
     * 对话框中编辑厂家
     * @param {Object} row - 厂家数据行
     */
    editManufacturerInDialog(row) {
      console.log('对话框中编辑厂家', row);
      // TODO: 实现对话框中编辑厂家功能
    },

    /**
     * 对话框中删除厂家
     * @param {Object} row - 厂家数据行
     */
    removeManufacturerInDialog(row) {
      console.log('对话框中删除厂家', row);
      // TODO: 实现对话框中删除厂家功能
    }
  }
};
</script>

<style scoped>
/* 产品管理页面特有样式 */

.el-textarea {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}
</style>
