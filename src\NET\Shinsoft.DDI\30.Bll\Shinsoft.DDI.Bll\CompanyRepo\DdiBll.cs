﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class DdiBll : BaseCompanyBll
    {
        #region Constructs

        public DdiBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public DdiBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public DdiBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public DdiBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs
    }
}