﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class DdiBll : BaseCompanyBll
    {
        #region Constructs

        public DdiBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public DdiBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public DdiBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public DdiBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs


        /// <summary>
        /// 保存结果
        /// </summary>
        /// <param name="salesFlowDataType"></param>
        /// <param name="dataXml"></param>
        public void SaveClientData(SalesFlowDataType salesFlowDataType, string dataXml)
        {
            //TODO 考虑dataXml处理的位置
            switch (salesFlowDataType)
            {
                case SalesFlowDataType.B:
                    this.SaveClientPurchase(dataXml);
                    break;
                case SalesFlowDataType.S:
                    this.SaveClientSalesFlow(dataXml);
                    break;
                case SalesFlowDataType.I:
                    this.SaveClientInventory(dataXml);
                    break;
                default:
                    break;
            }

        }

        /// <summary>
        /// 保存购进数据
        /// </summary>
        /// <param name="dataXml"></param>
        private void SaveClientPurchase(string dataXml)
        {


        }

        /// <summary>
        /// 保存销售数据
        /// </summary>
        /// <param name="dataXml"></param>
        private void SaveClientSalesFlow(string dataXml)
        {


        }

        /// <summary>
        /// 保存库存数据
        /// </summary>
        /// <param name="dataXml"></param>
        private void SaveClientInventory(string dataXml)
        {


        }

        /// <summary>
        /// 保存客户端日志
        /// </summary>
        /// <param name="logInfo">日志内容</param>
        /// <param name="logTime">日志时间</param>
        public void SaveClientLog(string logInfo, string logTime)
        {


        }
    }
}