CREATE TABLE [dbo].[ImportSalesFlowMasterTemp] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL  CONSTRAINT [Default_ImportSalesFlowMasterTemp_ID] DEFAULT (NEWSEQUENTIALID()),    
    [AttachmentId]              UNIQUEIDENTIFIER            NOT NULL,
    ReceiverId                  UNIQUEIDENTIFIER            NOT NULL,
    ShipperId                   UNIQUEIDENTIFIER            NOT NULL,
    [RightCount]                INT                         NOT NULL,
    [ErrorCount]                INT                         NOT NULL,
    [TotalCount]                INT                         NOT NULL,
    [HandledCount]              INT                         NOT NULL,
    [Operator]                  NVARCHAR(50)                NOT NULL,
    [ImportTime]                DATETIME                    NOT NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
    CONSTRAINT [PK_ImportSalesFlowMasterTemp] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ImportSalesFlowMasterTemp_Receiver_00_Receiver] FOREIGN KEY ([ReceiverId]) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_ImportSalesFlowMasterTemp_Receiver_01_Shipper] FOREIGN KEY (ShipperId) REFERENCES [dbo].[Receiver] ([ID]),
    )
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'销售流向导入主表临时表',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'附件ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'AttachmentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'正确数量',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'RightCount'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'错误数量',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'ErrorCount'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'总数量',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'TotalCount'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'已处理数量',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'HandledCount'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'操作人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'Operator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'导入时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'ImportTime'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowMasterTemp',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
GO
