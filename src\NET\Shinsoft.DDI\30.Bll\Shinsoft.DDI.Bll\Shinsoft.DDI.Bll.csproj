<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
	<FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
	<Reference Include="Shinsoft.Core">
	  <HintPath>..\..\00.Reference\net8.0\Shinsoft.Core.dll</HintPath>
	</Reference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="MailKit" Version="4.11.0" />
    <PackageReference Include="NLog" Version="5.4.0" />
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\..\10.EntityFrameworkCore\Shinsoft.DDI.Dal\Shinsoft.DDI.Dal.csproj" />
	<ProjectReference Include="..\..\10.EntityFrameworkCore\Shinsoft.DDI.Entities\Shinsoft.DDI.Entities.csproj" />
	<ProjectReference Include="..\..\20.Common\Shinsoft.DDI.Common\Shinsoft.DDI.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
	<Using Include="System.ComponentModel" />
	<Using Include="System.Linq.Expressions" />
	<Using Include="System.Transactions" />
	<Using Include="Shinsoft.Core" />
	<Using Include="Shinsoft.Core.EntityFrameworkCore" />
	<Using Include="Shinsoft.Core.Hosting" />
	<Using Include="Shinsoft.Core.Mvc" />
	<Using Include="Shinsoft.Core.NLog" />
	<Using Include="Shinsoft.Core.Json" />
	<Using Include="Shinsoft.DDI.Common" />
	<Using Include="Shinsoft.DDI.Common.Configration" />
	<Using Include="Shinsoft.DDI.Entities" />
	<Using Include="Shinsoft.DDI.Bll.Caching" />
  </ItemGroup>

</Project>
